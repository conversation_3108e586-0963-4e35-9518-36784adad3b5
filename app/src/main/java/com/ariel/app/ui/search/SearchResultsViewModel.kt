package com.ariel.app.ui.search

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ariel.app.data.model.Event
import com.ariel.app.data.model.FAQ
import com.ariel.app.data.model.Knowledge
import com.ariel.app.data.model.SearchResult
import com.ariel.app.data.model.Ticket
import com.ariel.app.data.repository.SearchRepository
import kotlinx.coroutines.launch

/**
 * ViewModel for the search results screen.
 */
class SearchResultsViewModel : ViewModel() {

    private val searchRepository = SearchRepository()

    private val _searchResults = MutableLiveData<SearchResult>()
    val searchResults: LiveData<SearchResult> = _searchResults

    private val _tickets = MutableLiveData<List<Ticket>>()
    val tickets: LiveData<List<Ticket>> = _tickets

    private val _events = MutableLiveData<List<Event>>()
    val events: LiveData<List<Event>> = _events

    private val _knowledges = MutableLiveData<List<Knowledge>>()
    val knowledges: LiveData<List<Knowledge>> = _knowledges

    private val _faqs = MutableLiveData<List<FAQ>>()
    val faqs: LiveData<List<FAQ>> = _faqs

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error

    private val _searchQuery = MutableLiveData<String>()
    val searchQuery: LiveData<String> = _searchQuery

    /**
     * Performs a search with the given query.
     *
     * @param token The authentication token.
     * @param query The search query string.
     */
    fun search(token: String, query: String) {
        _isLoading.value = true
        _error.value = null
        _searchQuery.value = query

        viewModelScope.launch {
            val result = searchRepository.search(token, query)
            _isLoading.value = false

            result.fold(
                onSuccess = { searchResult ->
                    _searchResults.value = searchResult
                    _tickets.value = searchResult.matchedTickets
                    _events.value = searchResult.matchedEvents
                    _knowledges.value = searchResult.matchedKnowledges
                    _faqs.value = searchResult.matchedFaqs
                },
                onFailure = { exception ->
                    _error.value = exception.message
                }
            )
        }
    }
}
