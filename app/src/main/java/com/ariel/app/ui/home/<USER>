package com.ariel.app.ui.home

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ariel.app.data.SessionManager
import com.ariel.app.data.api.RetrofitClient
import com.ariel.app.data.model.EventGroup
import com.ariel.app.data.model.Ticket
import com.ariel.app.data.model.TicketStatistics

import kotlinx.coroutines.launch

class HomeViewModel(private val sessionManager: SessionManager) : ViewModel() {

    private val apiService = RetrofitClient.getApiService()

    private val _ticketStatistics = MutableLiveData<List<TicketStatistics>>()
    val ticketStatistics: LiveData<List<TicketStatistics>> = _ticketStatistics

    private val _groups = MutableLiveData<List<EventGroup>>()
    val groups: LiveData<List<EventGroup>> = _groups

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    // Filtered tickets state
    private val _filteredTickets = MutableLiveData<List<Ticket>>()
    val filteredTickets: LiveData<List<Ticket>> = _filteredTickets

    private val _isLoadingTickets = MutableLiveData<Boolean>()
    val isLoadingTickets: LiveData<Boolean> = _isLoadingTickets

    private val _ticketsError = MutableLiveData<String?>()
    val ticketsError: LiveData<String?> = _ticketsError

    private val _selectedStatus = MutableLiveData<String?>()
    val selectedStatus: LiveData<String?> = _selectedStatus

    private val _selectedStatusLabel = MutableLiveData<String?>()
    val selectedStatusLabel: LiveData<String?> = _selectedStatusLabel

    private val _selectedGroup = MutableLiveData<EventGroup?>()
    val selectedGroup: LiveData<EventGroup?> = _selectedGroup

    // Remove automatic loading from init - let Fragment control when to load

    /**
     * Loads homepage data including ticket statistics and groups from the API.
     */
    fun loadHomepageData() {
        Log.d("HomeViewModel", "Loading homepage data...")

        val token = sessionManager.getAuthToken()
        if (token == null) {
            Log.e("HomeViewModel", "No auth token available")
            _ticketStatistics.value = emptyList()
            _groups.value = emptyList()
            return
        }

        viewModelScope.launch {
            _isLoading.value = true

            try {
                val response = apiService.getHomepageData("Token $token")
                Log.d("HomeViewModel", "API response code: ${response.code()}")

                if (response.isSuccessful) {
                    val homepageData = response.body()
                    if (homepageData != null) {
                        val statistics = homepageData.getTicketStatisticsList()
                        Log.d("HomeViewModel", "Loaded ${statistics.size} statistics")
                        _ticketStatistics.value = statistics

                        Log.d("HomeViewModel", "Loaded ${homepageData.groups.size} groups")
                        _groups.value = homepageData.groups
                    } else {
                        Log.e("HomeViewModel", "Response body is null")
                        _ticketStatistics.value = emptyList()
                        _groups.value = emptyList()
                    }
                } else {
                    Log.e("HomeViewModel", "API call failed: ${response.code()} - ${response.message()}")
                    _ticketStatistics.value = emptyList()
                    _groups.value = emptyList()
                }
            } catch (e: Exception) {
                Log.e("HomeViewModel", "Error loading homepage data", e)
                _ticketStatistics.value = emptyList()
                _groups.value = emptyList()
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Refreshes homepage data.
     */
    fun refreshHomepageData() {
        loadHomepageData()
    }

    /**
     * Fetches tickets filtered by status.
     */
    fun fetchTicketsByStatus(status: String, statusLabel: String) {
        Log.d("HomeViewModel", "Fetching tickets for status: $status ($statusLabel)")

        val token = sessionManager.getAuthToken()
        if (token == null) {
            _ticketsError.value = "Authentication token not found"
            return
        }

        _selectedStatus.value = status
        _selectedStatusLabel.value = statusLabel
        _isLoadingTickets.value = true
        _ticketsError.value = null

        viewModelScope.launch {
            try {
                val response = apiService.getMyTicketsByStatus("Token $token", status)

                if (response.isSuccessful) {
                    val tickets = response.body()?.results ?: emptyList()
                    Log.d("HomeViewModel", "Fetched ${tickets.size} tickets for status $status")
                    _filteredTickets.value = tickets
                } else {
                    val errorMsg = "Failed to fetch tickets: ${response.code()}"
                    Log.e("HomeViewModel", errorMsg)
                    _ticketsError.value = errorMsg
                    _filteredTickets.value = emptyList()
                }
            } catch (e: Exception) {
                Log.e("HomeViewModel", "Error fetching tickets by status", e)
                _ticketsError.value = "Error: ${e.message}"
                _filteredTickets.value = emptyList()
            } finally {
                _isLoadingTickets.value = false
            }
        }
    }

    /**
     * Clears the filtered tickets and selected status.
     */
    fun clearFilteredTickets() {
        _filteredTickets.value = emptyList()
        _selectedStatus.value = null
        _selectedStatusLabel.value = null
        _selectedGroup.value = null
        _ticketsError.value = null
    }



    /**
     * Fetches tickets filtered by group ID.
     */
    fun fetchTicketsByGroupId(group: EventGroup) {
        Log.d("HomeViewModel", "Fetching tickets for group: ${group.id} (${group.name})")

        val token = sessionManager.getAuthToken()
        if (token == null) {
            _ticketsError.value = "Authentication token not found"
            return
        }

        _selectedGroup.value = group
        _selectedStatus.value = null
        _selectedStatusLabel.value = null
        _isLoadingTickets.value = true
        _ticketsError.value = null

        viewModelScope.launch {
            try {
                val response = apiService.getMyTicketsByGroupId("Token $token", group.id)

                if (response.isSuccessful) {
                    val tickets = response.body()?.results ?: emptyList()
                    Log.d("HomeViewModel", "Fetched ${tickets.size} tickets for group ${group.id}")
                    _filteredTickets.value = tickets
                } else {
                    val errorMsg = "Failed to fetch tickets: ${response.code()}"
                    Log.e("HomeViewModel", errorMsg)
                    _ticketsError.value = errorMsg
                    _filteredTickets.value = emptyList()
                }
            } catch (e: Exception) {
                Log.e("HomeViewModel", "Error fetching tickets by group", e)
                _ticketsError.value = "Error: ${e.message}"
                _filteredTickets.value = emptyList()
            } finally {
                _isLoadingTickets.value = false
            }
        }
    }
}
