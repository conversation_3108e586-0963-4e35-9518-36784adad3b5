package com.ariel.app.ui.ticketdetails

import android.content.Context
import android.net.Uri
import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ariel.app.data.model.CategoryResponse
import com.ariel.app.data.model.Priority
import com.ariel.app.data.model.Status
import com.ariel.app.data.model.Ticket
import com.ariel.app.data.repository.TicketRepository
import kotlinx.coroutines.launch

/**
 * ViewModel for the ticket details screen.
 */
class TicketDetailsViewModel : ViewModel() {

    private val ticketRepository = TicketRepository()

    // State for the ticket details
    sealed class TicketDetailsState {
        object Loading : TicketDetailsState()
        data class Success(val ticket: Ticket) : TicketDetailsState()
        data class Error(val message: String) : TicketDetailsState()
    }

    // State for the reply operation
    sealed class ReplyState {
        object Idle : ReplyState()
        object Loading : ReplyState()
        data class Success(val ticket: Ticket) : ReplyState()
        data class Error(val message: String) : ReplyState()
    }

    // State for the category update operation
    sealed class CategoryUpdateState {
        object Idle : CategoryUpdateState()
        object Loading : CategoryUpdateState()
        data class Success(val ticket: Ticket) : CategoryUpdateState()
        data class Error(val message: String) : CategoryUpdateState()
    }

    // State for the priority update operation
    sealed class PriorityUpdateState {
        object Idle : PriorityUpdateState()
        object Loading : PriorityUpdateState()
        data class Success(val ticket: Ticket) : PriorityUpdateState()
        data class Error(val message: String) : PriorityUpdateState()
    }

    // State for the status update operation
    sealed class StatusUpdateState {
        object Idle : StatusUpdateState()
        object Loading : StatusUpdateState()
        data class Success(val ticket: Ticket) : StatusUpdateState()
        data class Error(val message: String) : StatusUpdateState()
    }

    // State for the delete operation
    sealed class DeleteState {
        object Idle : DeleteState()
        object Loading : DeleteState()
        object Success : DeleteState()
        data class Error(val message: String) : DeleteState()
    }

    // State for the rating operation
    sealed class RatingState {
        object Idle : RatingState()
        object Loading : RatingState()
        data class Success(val ticket: Ticket) : RatingState()
        data class Error(val message: String) : RatingState()
    }

    private val _state = MutableLiveData<TicketDetailsState>()
    val state: LiveData<TicketDetailsState> = _state

    private val _replyState = MutableLiveData<ReplyState>(ReplyState.Idle)
    val replyState: LiveData<ReplyState> = _replyState

    private val _categoryUpdateState = MutableLiveData<CategoryUpdateState>(CategoryUpdateState.Idle)
    val categoryUpdateState: LiveData<CategoryUpdateState> = _categoryUpdateState

    private val _priorityUpdateState = MutableLiveData<PriorityUpdateState>(PriorityUpdateState.Idle)
    val priorityUpdateState: LiveData<PriorityUpdateState> = _priorityUpdateState

    private val _statusUpdateState = MutableLiveData<StatusUpdateState>(StatusUpdateState.Idle)
    val statusUpdateState: LiveData<StatusUpdateState> = _statusUpdateState

    private val _deleteState = MutableLiveData<DeleteState>(DeleteState.Idle)
    val deleteState: LiveData<DeleteState> = _deleteState

    private val _ratingState = MutableLiveData<RatingState>(RatingState.Idle)
    val ratingState: LiveData<RatingState> = _ratingState

    private val _categories = MutableLiveData<List<CategoryResponse>>()
    val categories: LiveData<List<CategoryResponse>> = _categories

    private val _priorities = MutableLiveData<List<Priority>>()
    val priorities: LiveData<List<Priority>> = _priorities

    private val _statuses = MutableLiveData<List<Status>>()
    val statuses: LiveData<List<Status>> = _statuses

    private var _selectedFile = MutableLiveData<Uri?>(null)
    val selectedFile: LiveData<Uri?> = _selectedFile

    private val _authorGroups = MutableLiveData<List<String>>()
    val authorGroups: LiveData<List<String>> = _authorGroups

    private val _authorFullName = MutableLiveData<String>()
    val authorFullName: LiveData<String> = _authorFullName

    /**
     * Fetches the details of a specific ticket by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the ticket.
     */
    fun fetchTicketDetails(token: String, shortUuid: String) {
        _state.value = TicketDetailsState.Loading

        viewModelScope.launch {
            try {
                val result = ticketRepository.getTicketDetails(token, shortUuid)

                result.fold(
                    onSuccess = { ticket ->
                        _state.value = TicketDetailsState.Success(ticket)
                    },
                    onFailure = { exception ->
                        _state.value = TicketDetailsState.Error(
                            exception.message ?: "Failed to load ticket details"
                        )
                    }
                )
            } catch (e: Exception) {
                _state.value = TicketDetailsState.Error(
                    e.message ?: "An unexpected error occurred"
                )
            }
        }
    }

    /**
     * Sets the selected file for the reply.
     *
     * @param uri The URI of the selected file.
     */
    fun setSelectedFile(uri: Uri?) {
        _selectedFile.value = uri
    }

    /**
     * Validates the reply message.
     *
     * @param message The message to validate.
     * @return True if the message is valid, false otherwise.
     */
    fun validateReplyMessage(message: String): Boolean {
        return message.isNotBlank()
    }

    /**
     * Replies to the ticket.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the parent ticket.
     * @param message The message content of the reply.
     */
    fun replyToTicket(token: String, shortUuid: String, message: String) {
        _replyState.value = ReplyState.Loading

        viewModelScope.launch {
            try {
                val result = ticketRepository.replyToTicket(token, shortUuid, message)

                result.fold(
                    onSuccess = { ticket ->
                        _replyState.value = ReplyState.Success(ticket)
                        // Refresh ticket details to show the new reply
                        fetchTicketDetails(token, shortUuid)
                    },
                    onFailure = { exception ->
                        _replyState.value = ReplyState.Error(
                            exception.message ?: "Failed to reply to ticket"
                        )
                    }
                )
            } catch (e: Exception) {
                _replyState.value = ReplyState.Error(
                    e.message ?: "An unexpected error occurred"
                )
            }
        }
    }

    /**
     * Replies to the ticket with a file attachment.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the parent ticket.
     * @param message The message content of the reply.
     * @param fileUri The URI of the file to attach.
     * @param context The context to access the content resolver.
     */
    fun replyToTicketWithFile(
        token: String,
        shortUuid: String,
        message: String,
        fileUri: Uri,
        context: Context
    ) {
        _replyState.value = ReplyState.Loading

        viewModelScope.launch {
            try {
                val result = ticketRepository.replyToTicketWithFile(
                    token,
                    shortUuid,
                    message,
                    fileUri,
                    context
                )

                result.fold(
                    onSuccess = { ticket ->
                        _replyState.value = ReplyState.Success(ticket)
                        // Clear selected file
                        _selectedFile.value = null
                        // Refresh ticket details to show the new reply
                        fetchTicketDetails(token, shortUuid)
                    },
                    onFailure = { exception ->
                        _replyState.value = ReplyState.Error(
                            exception.message ?: "Failed to reply to ticket with file"
                        )
                    }
                )
            } catch (e: Exception) {
                _replyState.value = ReplyState.Error(
                    e.message ?: "An unexpected error occurred"
                )
            }
        }
    }

    /**
     * Resets the reply state to idle.
     */
    fun resetReplyState() {
        _replyState.value = ReplyState.Idle
    }

    /**
     * Fetches the list of available categories.
     *
     * @param token The authentication token.
     */
    fun fetchCategories(token: String) {
        viewModelScope.launch {
            try {
                val result = ticketRepository.getCategories(token)

                result.fold(
                    onSuccess = { categoriesList ->
                        _categories.value = categoriesList
                    },
                    onFailure = { exception ->
                        // Just log the error, don't update UI state
                        // This is a secondary operation that shouldn't affect the main UI
                        println("Failed to fetch categories: ${exception.message}")
                    }
                )
            } catch (e: Exception) {
                println("An unexpected error occurred while fetching categories: ${e.message}")
            }
        }
    }

    /**
     * Updates the category of a specific ticket.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the ticket.
     * @param categoryId The ID of the new category.
     */
    fun updateTicketCategory(token: String, shortUuid: String, categoryId: Int) {
        _categoryUpdateState.value = CategoryUpdateState.Loading
        Log.d("TicketDetailsViewModel", "Updating category to ID: $categoryId for ticket: $shortUuid")

        viewModelScope.launch {
            try {
                val result = ticketRepository.setTicketCategory(token, shortUuid, categoryId)

                result.fold(
                    onSuccess = { ticket ->
                        Log.d("TicketDetailsViewModel", "Category update successful")
                        _categoryUpdateState.value = CategoryUpdateState.Success(ticket)
                        // Instead of directly updating the state, fetch the full ticket details again
                        // to ensure we have all the data
                        fetchTicketDetails(token, shortUuid)
                    },
                    onFailure = { exception ->
                        Log.e("TicketDetailsViewModel", "Category update failed: ${exception.message}")
                        _categoryUpdateState.value = CategoryUpdateState.Error(
                            exception.message ?: "Failed to update ticket category"
                        )
                    }
                )
            } catch (e: Exception) {
                Log.e("TicketDetailsViewModel", "Exception during category update: ${e.message}", e)
                _categoryUpdateState.value = CategoryUpdateState.Error(
                    e.message ?: "An unexpected error occurred"
                )
            }
        }
    }

    /**
     * Resets the category update state to idle.
     */
    fun resetCategoryUpdateState() {
        _categoryUpdateState.value = CategoryUpdateState.Idle
    }

    /**
     * Fetches the list of available priorities.
     *
     * @param token The authentication token.
     */
    fun fetchPriorities(token: String) {
        viewModelScope.launch {
            try {
                val result = ticketRepository.getPriorities(token)

                result.fold(
                    onSuccess = { prioritiesList ->
                        _priorities.value = prioritiesList
                    },
                    onFailure = { exception ->
                        // Just log the error, don't update UI state
                        // This is a secondary operation that shouldn't affect the main UI
                        Log.e("TicketDetailsViewModel", "Failed to fetch priorities: ${exception.message}")
                    }
                )
            } catch (e: Exception) {
                Log.e("TicketDetailsViewModel", "An unexpected error occurred while fetching priorities: ${e.message}")
            }
        }
    }

    /**
     * Updates the priority of a specific ticket.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the ticket.
     * @param priorityValue The value of the new priority.
     */
    fun updateTicketPriority(token: String, shortUuid: String, priorityValue: String) {
        _priorityUpdateState.value = PriorityUpdateState.Loading
        Log.d("TicketDetailsViewModel", "Updating priority to value: $priorityValue for ticket: $shortUuid")

        viewModelScope.launch {
            try {
                val result = ticketRepository.setTicketPriority(token, shortUuid, priorityValue)

                result.fold(
                    onSuccess = { ticket ->
                        Log.d("TicketDetailsViewModel", "Priority update successful")
                        _priorityUpdateState.value = PriorityUpdateState.Success(ticket)
                        // Instead of directly updating the state, fetch the full ticket details again
                        // to ensure we have all the data
                        fetchTicketDetails(token, shortUuid)
                    },
                    onFailure = { exception ->
                        Log.e("TicketDetailsViewModel", "Priority update failed: ${exception.message}")
                        _priorityUpdateState.value = PriorityUpdateState.Error(
                            exception.message ?: "Failed to update ticket priority"
                        )
                    }
                )
            } catch (e: Exception) {
                Log.e("TicketDetailsViewModel", "Exception during priority update: ${e.message}", e)
                _priorityUpdateState.value = PriorityUpdateState.Error(
                    e.message ?: "An unexpected error occurred"
                )
            }
        }
    }

    /**
     * Resets the priority update state to idle.
     */
    fun resetPriorityUpdateState() {
        _priorityUpdateState.value = PriorityUpdateState.Idle
    }

    /**
     * Fetches the list of available statuses.
     *
     * @param token The authentication token.
     */
    fun fetchStatuses(token: String) {
        viewModelScope.launch {
            try {
                val result = ticketRepository.getStatuses(token)

                result.fold(
                    onSuccess = { statusesList ->
                        _statuses.value = statusesList
                    },
                    onFailure = { exception ->
                        // Just log the error, don't update UI state
                        // This is a secondary operation that shouldn't affect the main UI
                        Log.e("TicketDetailsViewModel", "Failed to fetch statuses: ${exception.message}")
                    }
                )
            } catch (e: Exception) {
                Log.e("TicketDetailsViewModel", "An unexpected error occurred while fetching statuses: ${e.message}")
            }
        }
    }

    /**
     * Updates the status of a specific ticket.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the ticket.
     * @param statusValue The value of the new status.
     */
    fun updateTicketStatus(token: String, shortUuid: String, statusValue: String) {
        _statusUpdateState.value = StatusUpdateState.Loading
        Log.d("TicketDetailsViewModel", "Updating status to value: $statusValue for ticket: $shortUuid")

        viewModelScope.launch {
            try {
                val result = ticketRepository.setTicketStatus(token, shortUuid, statusValue)

                result.fold(
                    onSuccess = { ticket ->
                        Log.d("TicketDetailsViewModel", "Status update successful")
                        _statusUpdateState.value = StatusUpdateState.Success(ticket)
                        // Instead of directly updating the state, fetch the full ticket details again
                        // to ensure we have all the data
                        fetchTicketDetails(token, shortUuid)
                    },
                    onFailure = { exception ->
                        Log.e("TicketDetailsViewModel", "Status update failed: ${exception.message}")
                        _statusUpdateState.value = StatusUpdateState.Error(
                            exception.message ?: "Failed to update ticket status"
                        )
                    }
                )
            } catch (e: Exception) {
                Log.e("TicketDetailsViewModel", "Exception during status update: ${e.message}", e)
                _statusUpdateState.value = StatusUpdateState.Error(
                    e.message ?: "An unexpected error occurred"
                )
            }
        }
    }

    /**
     * Resets the status update state to idle.
     */
    fun resetStatusUpdateState() {
        _statusUpdateState.value = StatusUpdateState.Idle
    }

    /**
     * Deletes a specific ticket by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the ticket to delete.
     */
    fun deleteTicket(token: String, shortUuid: String) {
        _deleteState.value = DeleteState.Loading
        Log.d("TicketDetailsViewModel", "Deleting ticket: $shortUuid")

        viewModelScope.launch {
            try {
                val result = ticketRepository.deleteTicket(token, shortUuid)

                result.fold(
                    onSuccess = {
                        Log.d("TicketDetailsViewModel", "Ticket deletion successful")
                        _deleteState.value = DeleteState.Success
                    },
                    onFailure = { exception ->
                        Log.e("TicketDetailsViewModel", "Ticket deletion failed: ${exception.message}")
                        _deleteState.value = DeleteState.Error(
                            exception.message ?: "Failed to delete ticket"
                        )
                    }
                )
            } catch (e: Exception) {
                Log.e("TicketDetailsViewModel", "Exception during ticket deletion: ${e.message}", e)
                _deleteState.value = DeleteState.Error(
                    e.message ?: "An unexpected error occurred"
                )
            }
        }
    }

    /**
     * Resets the delete state to idle.
     */
    fun resetDeleteState() {
        _deleteState.value = DeleteState.Idle
    }

    /**
     * Fetches the groups of a specific user by their short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the user.
     */
    fun fetchAuthorGroups(token: String, shortUuid: String) {
        viewModelScope.launch {
            try {
                val result = ticketRepository.getUserGroups(token, shortUuid)

                result.fold(
                    onSuccess = { groupsList ->
                        _authorGroups.value = groupsList
                    },
                    onFailure = { exception ->
                        // Just log the error, don't update UI state
                        // This is a secondary operation that shouldn't affect the main UI
                        Log.e("TicketDetailsViewModel", "Failed to fetch user groups: ${exception.message}")
                        // Set empty list to avoid null issues
                        _authorGroups.value = emptyList()
                    }
                )
            } catch (e: Exception) {
                Log.e("TicketDetailsViewModel", "An unexpected error occurred while fetching user groups: ${e.message}")
                // Set empty list to avoid null issues
                _authorGroups.value = emptyList()
            }
        }
    }

    /**
     * Fetches the full name of a specific user by their short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the user.
     */
    fun fetchAuthorFullName(token: String, shortUuid: String) {
        viewModelScope.launch {
            try {
                val result = ticketRepository.getUserFullName(token, shortUuid)

                result.fold(
                    onSuccess = { fullName ->
                        _authorFullName.value = fullName
                    },
                    onFailure = { exception ->
                        // Just log the error, don't update UI state
                        // This is a secondary operation that shouldn't affect the main UI
                        Log.e("TicketDetailsViewModel", "Failed to fetch user full name: ${exception.message}")
                        // Set empty string to avoid null issues
                        _authorFullName.value = ""
                    }
                )
            } catch (e: Exception) {
                Log.e("TicketDetailsViewModel", "An unexpected error occurred while fetching user full name: ${e.message}")
                // Set empty string to avoid null issues
                _authorFullName.value = ""
            }
        }
    }

    /**
     * Rates a specific ticket by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the ticket.
     * @param rating The rating value (1-5).
     */
    fun rateTicket(token: String, shortUuid: String, rating: Int) {
        _ratingState.value = RatingState.Loading
        Log.d("TicketDetailsViewModel", "Rating ticket: $shortUuid with rating: $rating")

        viewModelScope.launch {
            try {
                val result = ticketRepository.rateTicket(token, shortUuid, rating)

                result.fold(
                    onSuccess = { ticket ->
                        Log.d("TicketDetailsViewModel", "Rating update successful")
                        _ratingState.value = RatingState.Success(ticket)
                        // Refresh ticket details to show the updated rating
                        fetchTicketDetails(token, shortUuid)
                    },
                    onFailure = { exception ->
                        Log.e("TicketDetailsViewModel", "Rating update failed: ${exception.message}")
                        _ratingState.value = RatingState.Error(
                            exception.message ?: "Failed to rate ticket"
                        )
                    }
                )
            } catch (e: Exception) {
                Log.e("TicketDetailsViewModel", "Exception during rating update: ${e.message}", e)
                _ratingState.value = RatingState.Error(
                    e.message ?: "An unexpected error occurred"
                )
            }
        }
    }

    /**
     * Resets the rating state to idle.
     */
    fun resetRatingState() {
        _ratingState.value = RatingState.Idle
    }
}
