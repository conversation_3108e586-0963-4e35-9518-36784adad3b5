package com.ariel.app.data.repository

import android.content.Context
import android.net.Uri
import com.ariel.app.data.api.RetrofitClient
import com.ariel.app.data.model.CategoryResponse
import com.ariel.app.data.model.NewTicketRequest
import com.ariel.app.data.model.Priority
import com.ariel.app.data.model.Ticket
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.File
import java.io.FileOutputStream

/**
 * Repository class that handles new ticket-related operations.
 */
class NewTicketRepository {

    private val apiService = RetrofitClient.getApiService()

    /**
     * Retrieves the list of available categories.
     *
     * @param token The authentication token.
     * @return A Result containing either the list of categories or an Exception.
     */
    suspend fun getCategories(token: String): Result<List<CategoryResponse>> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val response = apiService.getCategories(authToken)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!.results)
                } else {
                    Result.failure(Exception("Failed to fetch categories: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Retrieves the list of available priorities.
     *
     * @param token The authentication token.
     * @return A Result containing either the list of priorities or an Exception.
     */
    suspend fun getPriorities(token: String): Result<List<Priority>> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val response = apiService.getPriorities(authToken)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to fetch priorities: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Creates a new ticket.
     *
     * @param token The authentication token.
     * @param title The title of the ticket.
     * @param message The message content of the ticket.
     * @param category The category of the ticket (persian_name).
     * @param priority The priority of the ticket (value).
     * @return A Result containing either the created ticket or an Exception.
     */
    suspend fun createTicket(
        token: String,
        title: String,
        message: String,
        category: String,
        priority: String
    ): Result<Ticket> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val newTicketRequest = NewTicketRequest(title, message, category, priority)
                val response = apiService.createTicket(authToken, newTicketRequest)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to create ticket: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Creates a new ticket with a file attachment.
     *
     * @param token The authentication token.
     * @param title The title of the ticket.
     * @param message The message content of the ticket.
     * @param category The category of the ticket (persian_name).
     * @param priority The priority of the ticket (value).
     * @param fileUri The URI of the file to attach.
     * @param context The context to use for accessing the content resolver.
     * @return A Result containing either the created ticket or an Exception.
     */
    suspend fun createTicketWithFile(
        token: String,
        title: String,
        message: String,
        category: String,
        priority: String,
        fileUri: Uri,
        context: Context
    ): Result<Ticket> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"

                // Convert the URI to a file
                val file = uriToFile(fileUri, context)

                // Create request parts
                val titlePart = title.toRequestBody("text/plain".toMediaTypeOrNull())
                val messagePart = message.toRequestBody("text/plain".toMediaTypeOrNull())
                val categoryPart = category.toRequestBody("text/plain".toMediaTypeOrNull())
                val priorityPart = priority.toRequestBody("text/plain".toMediaTypeOrNull())

                // Get file MIME type
                val mimeType = context.contentResolver.getType(fileUri) ?: "application/octet-stream"

                // Create file part
                val requestFile = file.asRequestBody(mimeType.toMediaTypeOrNull())
                val filePart = MultipartBody.Part.createFormData("file", file.name, requestFile)

                // Make API call
                val response = apiService.createTicketWithFile(
                    authToken,
                    titlePart,
                    messagePart,
                    categoryPart,
                    priorityPart,
                    filePart
                )

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to create ticket: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Converts a URI to a File.
     *
     * @param uri The URI to convert.
     * @param context The context to use for accessing the content resolver.
     * @return The File created from the URI.
     */
    private fun uriToFile(uri: Uri, context: Context): File {
        val inputStream = context.contentResolver.openInputStream(uri)
        val fileName = getFileNameFromUri(uri, context)
        val tempFile = File(context.cacheDir, fileName)

        tempFile.createNewFile()

        FileOutputStream(tempFile).use { outputStream ->
            inputStream?.copyTo(outputStream)
        }

        return tempFile
    }

    /**
     * Gets the file name from a URI.
     *
     * @param uri The URI to get the file name from.
     * @param context The context to use for accessing the content resolver.
     * @return The file name.
     */
    private fun getFileNameFromUri(uri: Uri, context: Context): String {
        var fileName = "file_${System.currentTimeMillis()}"

        // Try to get the file name from the content resolver
        context.contentResolver.query(uri, null, null, null, null)?.use { cursor ->
            if (cursor.moveToFirst()) {
                val displayNameIndex = cursor.getColumnIndex("_display_name")
                if (displayNameIndex != -1) {
                    val displayName = cursor.getString(displayNameIndex)
                    if (!displayName.isNullOrBlank()) {
                        fileName = displayName
                    }
                }
            }
        }

        return fileName
    }
}
