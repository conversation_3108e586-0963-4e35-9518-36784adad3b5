package com.ariel.app.ui.faqs

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ariel.app.data.model.FAQ
import com.ariel.app.data.repository.FAQRepository
import kotlinx.coroutines.launch

/**
 * ViewModel for the FAQs screen that handles FAQ data operations with pagination.
 */
class FAQsViewModel : ViewModel() {

    private val faqRepository = FAQRepository()

    private val _faqs = MutableLiveData<List<FAQ>>()
    val faqs: LiveData<List<FAQ>> = _faqs

    private val _isLoading = MutableLiveData<Boolean>(false)
    val isLoading: LiveData<Boolean> = _isLoading

    private val _isLoadingMore = MutableLiveData<Boolean>(false)
    val isLoadingMore: LiveData<Boolean> = _isLoadingMore

    private val _error = MutableLiveData<String?>(null)
    val error: LiveData<String?> = _error

    private var nextPageUrl: String? = null
    private var currentFAQs = mutableListOf<FAQ>()

    /**
     * Fetches the first page of FAQ items.
     *
     * @param token The authentication token.
     */
    fun fetchFAQs(token: String) {
        _isLoading.value = true
        _error.value = null
        currentFAQs.clear()

        viewModelScope.launch {
            try {
                val result = faqRepository.getFAQs(token, 15)

                result.fold(
                    onSuccess = { paginatedResponse ->
                        currentFAQs.addAll(paginatedResponse.results)
                        _faqs.value = currentFAQs.toList()
                        nextPageUrl = paginatedResponse.next
                    },
                    onFailure = { exception ->
                        _error.value = exception.message ?: "Unknown error occurred"
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message ?: "Unknown error occurred"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Loads more FAQ items from the next page if available.
     *
     * @param token The authentication token.
     */
    fun loadMoreFAQs(token: String) {
        if (nextPageUrl == null || _isLoadingMore.value == true) {
            return
        }

        _isLoadingMore.value = true

        viewModelScope.launch {
            try {
                val result = faqRepository.getFAQsFromUrl(token, nextPageUrl!!)

                result.fold(
                    onSuccess = { paginatedResponse ->
                        currentFAQs.addAll(paginatedResponse.results)
                        _faqs.value = currentFAQs.toList()
                        nextPageUrl = paginatedResponse.next
                    },
                    onFailure = { exception ->
                        _error.value = exception.message ?: "Unknown error occurred"
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message ?: "Unknown error occurred"
            } finally {
                _isLoadingMore.value = false
            }
        }
    }

    /**
     * Checks if there are more pages to load.
     *
     * @return True if there are more pages, false otherwise.
     */
    fun hasMorePages(): Boolean {
        return nextPageUrl != null
    }

    /**
     * Clears any error messages.
     */
    fun clearError() {
        _error.value = null
    }
}
