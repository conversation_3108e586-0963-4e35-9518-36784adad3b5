package com.ariel.app.ui.knowledges

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.OnBackPressedCallback
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ariel.app.R
import com.ariel.app.data.SessionManager
import com.ariel.app.databinding.FragmentKnowledgesBinding

/**
 * Fragment for displaying a list of knowledge items.
 */
class KnowledgesFragment : Fragment() {

    private var _binding: FragmentKnowledgesBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: KnowledgesViewModel
    private lateinit var knowledgeAdapter: KnowledgeAdapter
    private lateinit var sessionManager: SessionManager

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        viewModel = ViewModelProvider(this).get(KnowledgesViewModel::class.java)
        _binding = FragmentKnowledgesBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sessionManager = SessionManager(requireContext())
        setupRecyclerView()
        setupNewKnowledgeButton()
        observeViewModel()
        fetchKnowledges()
        setupBackNavigation()
    }

    /**
     * Sets up the new knowledge button.
     * Only shows the button for superusers.
     */
    private fun setupNewKnowledgeButton() {
        val user = sessionManager.getUser()
        if (user != null && user.isSuperuser) {
            // Only superusers can create knowledge items
            binding.fabNewKnowledge.setOnClickListener {
                findNavController().navigate(R.id.action_nav_knowledges_to_nav_new_knowledge)
            }
            binding.fabNewKnowledge.visibility = View.VISIBLE
        } else {
            // Hide the button for regular users
            binding.fabNewKnowledge.visibility = View.GONE
        }
    }

    /**
     * Sets up back navigation handling.
     */
    private fun setupBackNavigation() {
        requireActivity().onBackPressedDispatcher.addCallback(
            viewLifecycleOwner,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    // If we're at a top-level destination, let the system handle back
                    findNavController().navigateUp()
                }
            }
        )
    }

    /**
     * Sets up the RecyclerView for displaying knowledge items with pagination.
     */
    private fun setupRecyclerView() {
        knowledgeAdapter = KnowledgeAdapter { knowledge ->
            // Navigate to knowledge details
            val bundle = Bundle().apply {
                putString("shortUuid", knowledge.shortUuid)
            }
            findNavController().navigate(R.id.action_nav_knowledges_to_knowledgeDetailsFragment, bundle)
        }

        val layoutManager = LinearLayoutManager(requireContext())
        binding.recyclerKnowledges.apply {
            this.layoutManager = layoutManager
            adapter = knowledgeAdapter

            // Add scroll listener for pagination
            addOnScrollListener(object : RecyclerView.OnScrollListener() {
                override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                    super.onScrolled(recyclerView, dx, dy)

                    val visibleItemCount = layoutManager.childCount
                    val totalItemCount = layoutManager.itemCount
                    val firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition()

                    // Check if we're near the bottom and should load more
                    val isLoadingMore = viewModel.isLoadingMore.value ?: false
                    if (!isLoadingMore && viewModel.hasMorePages()) {
                        if ((visibleItemCount + firstVisibleItemPosition) >= totalItemCount - 3) {
                            // Load more when 3 items from the bottom
                            loadMoreKnowledges()
                        }
                    }
                }
            })
        }
    }

    /**
     * Observes changes in the ViewModel's LiveData.
     */
    private fun observeViewModel() {
        viewModel.knowledges.observe(viewLifecycleOwner) { knowledges ->
            knowledgeAdapter.submitList(knowledges)

            // Show empty view if the list is empty
            if (knowledges.isEmpty()) {
                binding.tvEmpty.visibility = View.VISIBLE
                binding.recyclerKnowledges.visibility = View.GONE
            } else {
                binding.tvEmpty.visibility = View.GONE
                binding.recyclerKnowledges.visibility = View.VISIBLE
            }
        }

        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE

            if (isLoading) {
                binding.tvError.visibility = View.GONE
                binding.tvEmpty.visibility = View.GONE
            }
        }

        viewModel.isLoadingMore.observe(viewLifecycleOwner) { isLoadingMore ->
            binding.progressBarPagination.visibility = if (isLoadingMore) View.VISIBLE else View.GONE
        }

        viewModel.error.observe(viewLifecycleOwner) { errorMessage ->
            if (errorMessage != null) {
                binding.tvError.text = errorMessage
                binding.tvError.visibility = View.VISIBLE
                binding.recyclerKnowledges.visibility = View.GONE
                binding.tvEmpty.visibility = View.GONE
            } else {
                binding.tvError.visibility = View.GONE
            }
        }
    }

    /**
     * Fetches knowledge items from the API.
     */
    private fun fetchKnowledges() {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.fetchKnowledges(token)
        } else {
            binding.tvError.text = getString(R.string.authentication_token_not_found)
            binding.tvError.visibility = View.VISIBLE
            binding.recyclerKnowledges.visibility = View.GONE
        }
    }

    /**
     * Loads more knowledge items for pagination.
     */
    private fun loadMoreKnowledges() {
        val token = sessionManager.getAuthToken()
        if (token != null) {
            viewModel.loadMoreKnowledges(token)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
