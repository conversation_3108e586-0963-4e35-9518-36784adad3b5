plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.navigation.safeargs)
    id("kotlin-parcelize")
}

android {
    namespace = "com.ariel.app"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.ariel.app"  // نام برنامه در مارکت
        minSdk = 33
        targetSdk = 35
        versionCode = 1        // کد نسخه (عدد صحیح - برای مارکت مهمه)
        versionName = "1.0.0"  // نام نسخه (به کاربر نمایش داده میشه)

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    // __BY_ME__
    // conditionally set url
    flavorDimensions += "default"
    productFlavors {
        create("emulator") {
            dimension = "default"
            buildConfigField("String", "BASE_URL", "\"http://10.0.2.2:8000\"")
        }
        create("phone") {
            dimension = "default"
            buildConfigField("String", "BASE_URL", "\"http://192.168.1.100:8000\"")
        }
        create("production") {
            dimension = "default"
            buildConfigField("String", "BASE_URL", "\"https://tk.rahavardit.ir\"")
        }
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }
    buildFeatures {
        viewBinding = true
        buildConfig = true
    }
}

dependencies {

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.appcompat)
    implementation(libs.material)
    implementation(libs.androidx.constraintlayout)
    implementation(libs.androidx.lifecycle.livedata.ktx)
    implementation(libs.androidx.lifecycle.viewmodel.ktx)
    implementation(libs.androidx.navigation.fragment.ktx)
    implementation(libs.androidx.navigation.ui.ktx)

    // Network
    implementation(libs.retrofit)
    implementation(libs.retrofit.gson)
    implementation(libs.okhttp)
    implementation(libs.okhttp.logging)
    implementation(libs.gson)

    // Coroutines
    implementation(libs.kotlinx.coroutines.core)
    implementation(libs.kotlinx.coroutines.android)

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
}
