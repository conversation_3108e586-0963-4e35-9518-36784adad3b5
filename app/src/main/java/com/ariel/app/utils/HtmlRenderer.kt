package com.ariel.app.utils

import android.graphics.Color
import android.graphics.Typeface
import android.text.Html
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.method.LinkMovementMethod
import android.text.style.BackgroundColorSpan
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.text.style.UnderlineSpan
import android.widget.TextView
import java.util.regex.Pattern

/**
 * Simple HTML renderer that adds CSS support to Android's Html.fromHtml.
 */
object HtmlRenderer {

    /**
     * Applies HTML content to a TextView with CSS support and working links.
     */
    fun applyToTextView(textView: TextView, htmlContent: String) {
        // Let Android handle HTML including links
        val spanned = Html.fromHtml(htmlContent, Html.FROM_HTML_MODE_LEGACY)
        val spannable = SpannableStringBuilder(spanned)

        // Add our custom CSS styling
        applyCssStyles(htmlContent, spannable)

        // Apply with link support
        textView.text = spannable
        textView.movementMethod = LinkMovementMethod.getInstance()

        // Handle RecyclerView touch conflicts
        textView.setOnTouchListener { v, event ->
            val result = textView.movementMethod?.onTouchEvent(textView, spannable, event) ?: false
            if (result) true else false
        }
    }

    /**
     * Applies CSS styles from span tags to the spannable text.
     */
    private fun applyCssStyles(htmlContent: String, spannable: SpannableStringBuilder) {
        // Simple pattern to match span tags with style attributes
        val spanPattern = Pattern.compile(
            "<span\\s+style\\s*=\\s*[\"']([^\"']*)[\"'][^>]*>([^<]*)</span>",
            Pattern.CASE_INSENSITIVE
        )

        val matcher = spanPattern.matcher(htmlContent)

        while (matcher.find()) {
            val styleAttr = matcher.group(1) ?: ""
            val textContent = matcher.group(2) ?: ""

            // Find this text in the spannable
            val textStart = spannable.indexOf(textContent)
            if (textStart >= 0) {
                val textEnd = textStart + textContent.length
                parseAndApplyStyles(styleAttr, spannable, textStart, textEnd)
            }
        }
    }

    /**
     * Parses CSS style string and applies styles to the spannable.
     */
    private fun parseAndApplyStyles(
        styleAttr: String,
        spannable: SpannableStringBuilder,
        start: Int,
        end: Int
    ) {
        // Split style declarations by semicolon
        val declarations = styleAttr.split(";")

        for (declaration in declarations) {
            val parts = declaration.split(":")
            if (parts.size == 2) {
                val property = parts[0].trim().lowercase()
                val value = parts[1].trim()

                when (property) {
                    "color" -> {
                        val color = parseColor(value)
                        if (color != null) {
                            spannable.setSpan(
                                ForegroundColorSpan(color),
                                start,
                                end,
                                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                            )
                        }
                    }
                    "background-color", "background" -> {
                        val color = parseColor(value)
                        if (color != null) {
                            spannable.setSpan(
                                BackgroundColorSpan(color),
                                start,
                                end,
                                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                            )
                        }
                    }
                    "font-weight" -> {
                        if (value.lowercase() == "bold") {
                            spannable.setSpan(
                                StyleSpan(Typeface.BOLD),
                                start,
                                end,
                                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                            )
                        }
                    }
                    "font-style" -> {
                        if (value.lowercase() == "italic") {
                            spannable.setSpan(
                                StyleSpan(Typeface.ITALIC),
                                start,
                                end,
                                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                            )
                        }
                    }
                    "text-decoration" -> {
                        if (value.lowercase().contains("underline")) {
                            spannable.setSpan(
                                UnderlineSpan(),
                                start,
                                end,
                                Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                            )
                        }
                    }
                }
            }
        }
    }

    /**
     * Parses color values from CSS.
     */
    private fun parseColor(colorValue: String): Int? {
        val cleanValue = colorValue.trim().lowercase()

        return try {
            when {
                // Hex colors
                cleanValue.startsWith("#") -> {
                    Color.parseColor(cleanValue)
                }
                // RGB colors
                cleanValue.startsWith("rgb(") -> {
                    val rgbPattern = Pattern.compile("rgb\\s*\\(\\s*(\\d+)\\s*,\\s*(\\d+)\\s*,\\s*(\\d+)\\s*\\)")
                    val matcher = rgbPattern.matcher(cleanValue)
                    if (matcher.find()) {
                        val r = matcher.group(1)?.toIntOrNull() ?: return null
                        val g = matcher.group(2)?.toIntOrNull() ?: return null
                        val b = matcher.group(3)?.toIntOrNull() ?: return null
                        Color.rgb(r, g, b)
                    } else null
                }
                // Named colors
                else -> {
                    when (cleanValue) {
                        "red" -> Color.RED
                        "green" -> Color.GREEN
                        "blue" -> Color.BLUE
                        "black" -> Color.BLACK
                        "white" -> Color.WHITE
                        "yellow" -> Color.YELLOW
                        "orange" -> Color.parseColor("#FFA500")
                        "purple" -> Color.parseColor("#800080")
                        "pink" -> Color.parseColor("#FFC0CB")
                        "gray", "grey" -> Color.GRAY
                        else -> null
                    }
                }
            }
        } catch (e: Exception) {
            null
        }
    }
}
