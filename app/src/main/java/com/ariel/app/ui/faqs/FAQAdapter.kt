package com.ariel.app.ui.faqs

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.ariel.app.data.model.FAQ
import com.ariel.app.databinding.ItemFaqBinding

/**
 * Adapter for displaying FAQ items in a RecyclerView.
 *
 * @property onFAQClick Callback for when a FAQ item is clicked.
 */
class FAQAdapter(private val onFAQClick: (FAQ) -> Unit) :
    ListAdapter<FAQ, FAQAdapter.FAQViewHolder>(FAQDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FAQViewHolder {
        val binding = ItemFaqBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return FAQViewHolder(binding)
    }

    override fun onBindViewHolder(holder: <PERSON>QViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    /**
     * ViewHolder for a FAQ item.
     */
    inner class FAQViewHolder(private val binding: ItemFaqBinding) :
        RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onFAQClick(getItem(position))
                }
            }
        }

        /**
         * Binds a FAQ item to the ViewHolder.
         *
         * @param faq The FAQ item to bind.
         */
        fun bind(faq: FAQ) {
            binding.apply {
                tvFaqTitle.text = faq.title
                tvFaqId.text = faq.shortUuid
                tvCreatedDate.text = faq.createdJalali
            }
        }
    }

    /**
     * DiffUtil callback for comparing FAQ items.
     */
    private class FAQDiffCallback : DiffUtil.ItemCallback<FAQ>() {
        override fun areItemsTheSame(oldItem: FAQ, newItem: FAQ): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: FAQ, newItem: FAQ): Boolean {
            return oldItem == newItem
        }
    }
}
