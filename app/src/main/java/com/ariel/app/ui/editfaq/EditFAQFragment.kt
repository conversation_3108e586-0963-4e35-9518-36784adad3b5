package com.ariel.app.ui.editfaq

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.ariel.app.R
import com.ariel.app.data.SessionManager
import com.ariel.app.data.model.FAQ
import com.ariel.app.databinding.FragmentEditFaqBinding

/**
 * Fragment for editing an existing FAQ item.
 */
class EditFAQFragment : Fragment() {

    private var _binding: FragmentEditFaqBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: EditFAQViewModel
    private lateinit var sessionManager: SessionManager
    private var shortUuid: String? = null
    private var faq: FAQ? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        viewModel = ViewModelProvider(this).get(EditFAQViewModel::class.java)
        _binding = FragmentEditFaqBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sessionManager = SessionManager(requireContext())
        shortUuid = arguments?.getString("shortUuid")
        faq = arguments?.getParcelable("faq")

        // Check if user is a superuser
        if (!sessionManager.getUserIsSuperuser()) {
            // Only superusers can edit FAQ items, navigate back
            Toast.makeText(
                requireContext(),
                "Only administrators can edit FAQ items",
                Toast.LENGTH_SHORT
            ).show()
            findNavController().navigateUp()
            return
        }

        // Check if we have the FAQ data
        if (faq == null) {
            Toast.makeText(
                requireContext(),
                "FAQ data not found",
                Toast.LENGTH_SHORT
            ).show()
            findNavController().navigateUp()
            return
        }

        // Populate the form with the FAQ data
        populateForm()
        setupListeners()
        observeViewModel()
    }

    /**
     * Populates the form with the FAQ data.
     */
    private fun populateForm() {
        faq?.let {
            binding.etFaqTitle.setText(it.title)
            binding.etFaqBody.setText(it.body)
        }
    }

    /**
     * Sets up click listeners and other UI interactions.
     */
    private fun setupListeners() {
        binding.btnSubmit.setOnClickListener {
            val title = binding.etFaqTitle.text.toString().trim()
            val body = binding.etFaqBody.text.toString().trim()

            val validationResult = viewModel.validateInputs(title, body)

            when {
                !validationResult.isTitleValid -> {
                    binding.tilFaqTitle.error = getString(R.string.please_enter_title)
                }
                !validationResult.isBodyValid -> {
                    binding.tilFaqBody.error = getString(R.string.please_enter_body)
                }
                else -> {
                    // Clear any previous errors
                    binding.tilFaqTitle.error = null
                    binding.tilFaqBody.error = null

                    // Update FAQ
                    val token = sessionManager.getAuthToken()
                    if (token != null && shortUuid != null) {
                        viewModel.updateFAQ(token, shortUuid!!, title, body)
                    } else if (token == null) {
                        showError(getString(R.string.authentication_token_not_found))
                    } else {
                        showError(getString(R.string.faq_id_not_found))
                    }
                }
            }
        }
    }

    /**
     * Observes changes in the ViewModel's LiveData.
     */
    private fun observeViewModel() {
        viewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            binding.btnSubmit.isEnabled = !isLoading
        }

        viewModel.error.observe(viewLifecycleOwner) { errorMessage ->
            if (errorMessage != null) {
                showError(errorMessage)
            } else {
                binding.tvError.visibility = View.GONE
            }
        }

        viewModel.faqUpdateResult.observe(viewLifecycleOwner) { result ->
            when (result) {
                is EditFAQViewModel.FAQUpdateResult.Success -> {
                    Toast.makeText(
                        requireContext(),
                        getString(R.string.faq_updated_successfully),
                        Toast.LENGTH_SHORT
                    ).show()

                    // Navigate back to FAQ details
                    val bundle = Bundle().apply {
                        putString("shortUuid", result.faq.shortUuid)
                    }
                    findNavController().navigate(
                        R.id.action_editFAQFragment_to_faqDetailsFragment,
                        bundle
                    )
                }
                is EditFAQViewModel.FAQUpdateResult.Error -> {
                    showError(result.errorMessage)
                }
            }
        }
    }

    /**
     * Shows an error message.
     *
     * @param message The error message to show.
     */
    private fun showError(message: String) {
        binding.tvError.text = message
        binding.tvError.visibility = View.VISIBLE
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
