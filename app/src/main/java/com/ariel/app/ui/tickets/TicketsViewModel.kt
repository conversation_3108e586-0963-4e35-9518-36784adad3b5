package com.ariel.app.ui.tickets

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ariel.app.data.model.Ticket
import com.ariel.app.data.repository.TicketRepository
import kotlinx.coroutines.launch

/**
 * ViewModel for the tickets screen that handles ticket-related operations with pagination.
 */
class TicketsViewModel : ViewModel() {

    private val ticketRepository = TicketRepository()

    private val _tickets = MutableLiveData<List<Ticket>>()
    val tickets: LiveData<List<Ticket>> = _tickets

    private val _isLoading = MutableLiveData<Boolean>(false)
    val isLoading: LiveData<Boolean> = _isLoading

    private val _isLoadingMore = MutableLiveData<Boolean>(false)
    val isLoadingMore: LiveData<Boolean> = _isLoadingMore

    private val _error = MutableLiveData<String?>(null)
    val error: LiveData<String?> = _error

    private var nextPageUrl: String? = null
    private var currentTickets = mutableListOf<Ticket>()

    /**
     * Fetches the first page of tickets for the authenticated user.
     *
     * @param token The authentication token.
     */
    fun fetchTickets(token: String) {
        _isLoading.value = true
        _error.value = null
        currentTickets.clear()

        viewModelScope.launch {
            try {
                val result = ticketRepository.getMyTickets(token, 15)

                result.fold(
                    onSuccess = { paginatedResponse ->
                        currentTickets.addAll(paginatedResponse.results)
                        _tickets.value = currentTickets.toList()
                        nextPageUrl = paginatedResponse.next
                    },
                    onFailure = { exception ->
                        _error.value = exception.message ?: "Unknown error occurred"
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message ?: "Unknown error occurred"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Loads more tickets from the next page if available.
     *
     * @param token The authentication token.
     */
    fun loadMoreTickets(token: String) {
        if (nextPageUrl == null || _isLoadingMore.value == true) {
            return
        }

        _isLoadingMore.value = true

        viewModelScope.launch {
            try {
                val result = ticketRepository.getTicketsFromUrl(token, nextPageUrl!!)

                result.fold(
                    onSuccess = { paginatedResponse ->
                        currentTickets.addAll(paginatedResponse.results)
                        _tickets.value = currentTickets.toList()
                        nextPageUrl = paginatedResponse.next
                    },
                    onFailure = { exception ->
                        _error.value = exception.message ?: "Unknown error occurred"
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message ?: "Unknown error occurred"
            } finally {
                _isLoadingMore.value = false
            }
        }
    }

    /**
     * Checks if there are more pages to load.
     *
     * @return True if there are more pages, false otherwise.
     */
    fun hasMorePages(): Boolean {
        return nextPageUrl != null
    }

    /**
     * Fetches tickets filtered by status for the authenticated user.
     *
     * @param token The authentication token.
     * @param status The status to filter by.
     */
    fun fetchTicketsByStatus(token: String, status: String) {
        _isLoading.value = true
        _error.value = null
        currentTickets.clear()

        viewModelScope.launch {
            try {
                val result = ticketRepository.getMyTicketsByStatus(token, status)

                result.fold(
                    onSuccess = { paginatedResponse ->
                        currentTickets.addAll(paginatedResponse.results)
                        _tickets.value = currentTickets.toList()
                        nextPageUrl = paginatedResponse.next
                    },
                    onFailure = { exception ->
                        _error.value = exception.message ?: "Unknown error occurred"
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message ?: "Unknown error occurred"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Fetches tickets filtered by group ID for the authenticated user.
     *
     * @param token The authentication token.
     * @param groupId The group ID to filter by.
     */
    fun fetchTicketsByGroupId(token: String, groupId: Int) {
        _isLoading.value = true
        _error.value = null
        currentTickets.clear()

        viewModelScope.launch {
            try {
                val result = ticketRepository.getMyTicketsByGroupId(token, groupId)

                result.fold(
                    onSuccess = { paginatedResponse ->
                        currentTickets.addAll(paginatedResponse.results)
                        _tickets.value = currentTickets.toList()
                        nextPageUrl = paginatedResponse.next
                    },
                    onFailure = { exception ->
                        _error.value = exception.message ?: "Unknown error occurred"
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message ?: "Unknown error occurred"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Clears any error messages.
     */
    fun clearError() {
        _error.value = null
    }
}
