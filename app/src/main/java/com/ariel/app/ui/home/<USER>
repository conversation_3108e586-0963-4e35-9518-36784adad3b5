package com.ariel.app.ui.home

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.ariel.app.data.model.EventGroup
import com.ariel.app.databinding.ItemGroupBinding

class GroupAdapter(
    private val onGroupClick: (EventGroup) -> Unit
) : ListAdapter<EventGroup, GroupAdapter.GroupViewHolder>(GroupDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): GroupViewHolder {
        val binding = ItemGroupBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return GroupViewHolder(binding)
    }

    override fun onBindViewHolder(holder: GroupViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class GroupViewHolder(
        private val binding: ItemGroupBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(group: EventGroup) {
            binding.tvGroupName.text = group.name

            binding.root.setOnClickListener {
                onGroupClick(group)
            }
        }
    }

    class GroupDiffCallback : DiffUtil.ItemCallback<EventGroup>() {
        override fun areItemsTheSame(oldItem: EventGroup, newItem: EventGroup): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: EventGroup, newItem: EventGroup): Boolean {
            return oldItem == newItem
        }
    }
}
