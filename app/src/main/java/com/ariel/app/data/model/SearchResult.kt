package com.ariel.app.data.model

import com.google.gson.annotations.SerializedName

/**
 * Data class representing the search results from the API.
 *
 * @property matchedTickets List of tickets matching the search query.
 * @property matchedEvents List of events matching the search query.
 * @property matchedKnowledges List of knowledge items matching the search query.
 * @property matchedFaqs List of FAQ items matching the search query.
 * @property queriedString The search query string that was used.
 */
data class SearchResult(
    @SerializedName("matched_tickets") val matchedTickets: List<Ticket>,
    @SerializedName("matched_events") val matchedEvents: List<Event>,
    @SerializedName("matched_knowledges") val matchedKnowledges: List<Knowledge>,
    @SerializedName("matched_faqs") val matchedFaqs: List<FAQ>,
    @SerializedName("queried_string") val queriedString: String
)
