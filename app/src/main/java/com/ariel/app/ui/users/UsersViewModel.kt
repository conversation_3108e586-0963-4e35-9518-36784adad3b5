package com.ariel.app.ui.users

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ariel.app.data.model.UserListItem
import com.ariel.app.data.repository.UserRepository
import kotlinx.coroutines.launch

/**
 * ViewModel for the users screen that handles user data operations.
 */
class UsersViewModel : ViewModel() {

    private val userRepository = UserRepository()
    private val TAG = "UsersViewModel"

    private val _users = MutableLiveData<List<UserListItem>>()
    val users: LiveData<List<UserListItem>> = _users

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error

    /**
     * Fetches the list of all users.
     *
     * @param token The authentication token.
     */
    fun fetchUsers(token: String) {
        _isLoading.value = true
        _error.value = null

        viewModelScope.launch {
            val result = userRepository.getUsers(token)

            result.fold(
                onSuccess = { usersList ->
                    _users.value = usersList
                    _isLoading.value = false
                },
                onFailure = { exception ->
                    Log.e(TAG, "Error fetching users", exception)
                    _error.value = exception.message ?: "Unknown error occurred"
                    _isLoading.value = false
                }
            )
        }
    }

    /**
     * Fetches the categories of a specific user by their short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the user.
     * @return A Result containing either the list of categories or an Exception.
     */
    suspend fun getUserCategories(token: String, shortUuid: String): Result<List<String>> {
        return userRepository.getUserCategories(token, shortUuid)
    }

    /**
     * Fetches the groups of a specific user by their short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the user.
     * @return A Result containing either the list of groups or an Exception.
     */
    suspend fun getUserGroups(token: String, shortUuid: String): Result<List<String>> {
        return userRepository.getUserGroups(token, shortUuid)
    }
}
