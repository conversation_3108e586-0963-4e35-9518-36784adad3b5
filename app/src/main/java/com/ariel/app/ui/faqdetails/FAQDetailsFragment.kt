package com.ariel.app.ui.faqdetails

import android.os.Bundle
import android.text.Html
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import android.widget.Toast

import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.ariel.app.R
import com.ariel.app.data.SessionManager
import com.ariel.app.data.model.FAQ
import com.ariel.app.databinding.FragmentFaqDetailsBinding
import com.ariel.app.utils.HtmlRenderer

/**
 * Fragment for displaying the details of a FAQ item.
 */
class FAQDetailsFragment : Fragment() {

    private var _binding: FragmentFaqDetailsBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: FAQDetailsViewModel
    private lateinit var sessionManager: SessionManager
    private var shortUuid: String? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        viewModel = ViewModelProvider(this).get(FAQDetailsViewModel::class.java)
        _binding = FragmentFaqDetailsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sessionManager = SessionManager(requireContext())
        shortUuid = arguments?.getString("shortUuid")

        observeViewModel()
        fetchFAQDetails()
        setupDeleteButton()
        setupEditButton()
    }

    /**
     * Observes changes in the ViewModel's LiveData.
     */
    private fun observeViewModel() {
        viewModel.faqState.observe(viewLifecycleOwner) { state ->
            when (state) {
                is FAQDetailsViewModel.FAQState.Loading -> {
                    showLoading()
                }
                is FAQDetailsViewModel.FAQState.Success -> {
                    hideLoading()
                    displayFAQ(state.faq)
                }
                is FAQDetailsViewModel.FAQState.Error -> {
                    hideLoading()
                    showError(state.errorMessage)
                }
            }
        }

        viewModel.deleteState.observe(viewLifecycleOwner) { state ->
            when (state) {
                is FAQDetailsViewModel.DeleteState.Loading -> {
                    showLoading()
                }
                is FAQDetailsViewModel.DeleteState.Success -> {
                    hideLoading()
                    Toast.makeText(requireContext(), getString(R.string.faq_deleted_successfully), Toast.LENGTH_SHORT).show()
                    // Navigate to home after deletion
                    findNavController().navigate(R.id.nav_home)
                }
                is FAQDetailsViewModel.DeleteState.Error -> {
                    hideLoading()
                    Toast.makeText(requireContext(), state.errorMessage, Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    /**
     * Displays the FAQ details in the UI.
     *
     * @param faq The FAQ item to display.
     */
    private fun displayFAQ(faq: FAQ) {
        binding.apply {
            cardFaq.visibility = View.VISIBLE
            tvFaqTitle.text = faq.title
            tvFaqDate.text = faq.createdJalali
            tvFaqId.text = getString(R.string.id_format, faq.shortUuid)
            HtmlRenderer.applyToTextView(tvFaqBody, faq.body)

            // Show edit and delete buttons only for superusers
            if (sessionManager.getUserIsSuperuser()) {
                btnEditFaq.visibility = View.VISIBLE
                btnDeleteFaq.visibility = View.VISIBLE
            } else {
                btnEditFaq.visibility = View.GONE
                btnDeleteFaq.visibility = View.GONE
            }
        }
    }

    /**
     * Shows the loading indicator and hides other views.
     */
    private fun showLoading() {
        binding.progressBar.visibility = View.VISIBLE
        binding.cardFaq.visibility = View.GONE
        binding.tvError.visibility = View.GONE
    }

    /**
     * Hides the loading indicator.
     */
    private fun hideLoading() {
        binding.progressBar.visibility = View.GONE
    }

    /**
     * Shows an error message.
     *
     * @param message The error message to display.
     */
    private fun showError(message: String) {
        binding.tvError.text = message
        binding.tvError.visibility = View.VISIBLE
        binding.cardFaq.visibility = View.GONE
    }

    /**
     * Fetches FAQ details from the API.
     */
    private fun fetchFAQDetails() {
        val token = sessionManager.getAuthToken()
        if (token != null && shortUuid != null) {
            viewModel.fetchFAQDetails(token, shortUuid!!)
        } else if (token == null) {
            showError(getString(R.string.authentication_token_not_found))
        } else {
            showError(getString(R.string.faq_id_not_found))
        }
    }

    /**
     * Sets up the delete button click listener.
     */
    private fun setupDeleteButton() {
        binding.btnDeleteFaq.setOnClickListener {
            showDeleteConfirmationDialog()
        }
    }

    /**
     * Shows a confirmation dialog before deleting the FAQ item.
     */
    private fun showDeleteConfirmationDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_delete_confirm, null)
        val titleTextView = dialogView.findViewById<TextView>(R.id.tv_dialog_title)
        val messageTextView = dialogView.findViewById<TextView>(R.id.tv_dialog_message)
        val confirmButton = dialogView.findViewById<Button>(R.id.btn_confirm)
        val cancelButton = dialogView.findViewById<Button>(R.id.btn_cancel)

        // Set the title and message
        titleTextView.text = getString(R.string.delete_faq_confirmation_title)
        messageTextView.text = getString(R.string.delete_faq_confirmation_message)

        val dialog = AlertDialog.Builder(requireContext())
            .setView(dialogView)
            .setCancelable(true)
            .create()

        // Set up button click listeners
        confirmButton.setOnClickListener {
            deleteFAQ()
            dialog.dismiss()
        }

        cancelButton.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }

    /**
     * Deletes the FAQ item.
     */
    private fun deleteFAQ() {
        val token = sessionManager.getAuthToken()
        if (token != null && shortUuid != null) {
            viewModel.deleteFAQ(token, shortUuid!!)
        } else if (token == null) {
            Toast.makeText(requireContext(), getString(R.string.authentication_token_not_found), Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(requireContext(), getString(R.string.faq_id_not_found), Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * Sets up the edit button click listener.
     */
    private fun setupEditButton() {
        binding.btnEditFaq.setOnClickListener {
            navigateToEditFAQ()
        }
    }

    /**
     * Navigates to the edit FAQ screen.
     */
    private fun navigateToEditFAQ() {
        val faq = viewModel.currentFAQ.value
        if (faq != null && shortUuid != null) {
            val bundle = Bundle().apply {
                putString("shortUuid", shortUuid)
                putParcelable("faq", faq)
            }
            findNavController().navigate(
                R.id.action_faqDetailsFragment_to_editFAQFragment,
                bundle
            )
        } else {
            Toast.makeText(requireContext(), getString(R.string.faq_data_not_found), Toast.LENGTH_SHORT).show()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
