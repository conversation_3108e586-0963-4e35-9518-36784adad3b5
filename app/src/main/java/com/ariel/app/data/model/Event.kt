package com.ariel.app.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

/**
 * Data class representing an event group in the system.
 *
 * @property id The unique identifier for the group.
 * @property name The name of the group.
 */
@Parcelize
data class EventGroup(
    val id: Int,
    val name: String
) : Parcelable

/**
 * Data class representing an event in the system.
 *
 * @property id The unique identifier for the event.
 * @property title The title of the event.
 * @property group The group the event belongs to.
 * @property body The content body of the event.
 * @property edited Whether the event has been edited.
 * @property visitors List of visitor IDs who have viewed the event.
 * @property shortUuid The short UUID of the event.
 * @property created The creation timestamp of the event.
 * @property createdJalali The creation timestamp in Jalali calendar format.
 * @property updated The last update timestamp of the event.
 * @property file The file attached to the event, if any.
 * @property fileMimetype The MIME type of the attached file, if any.
 * @property hasFile Whether the event has an attached file.
 */
@Parcelize
data class Event(
    val id: Int,
    val title: String,
    val group: EventGroup,
    val body: String,
    val edited: Boolean,
    val visitors: List<Int>,
    @SerializedName("short_uuid") val shortUuid: String,
    val created: String,
    @SerializedName("created_jalali") val createdJalali: String,
    val updated: String,
    val file: String?,
    @SerializedName("file_mimetype") val fileMimetype: String?,
    @SerializedName("has_file") val hasFile: Boolean
) : Parcelable
