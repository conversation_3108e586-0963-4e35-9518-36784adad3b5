package com.ariel.app.ui.users

import android.util.Log
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.ariel.app.R
import com.ariel.app.data.SessionManager
import com.ariel.app.data.model.UserListItem
import com.ariel.app.databinding.ItemUserBinding
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * Adapter for displaying users in a RecyclerView.
 *
 * @property viewModel The ViewModel to use for fetching additional user data.
 * @property lifecycleOwner The LifecycleOwner to use for coroutine scope.
 */
class UserAdapter(
    private val viewModel: UsersViewModel,
    private val lifecycleOwner: LifecycleOwner
) : ListAdapter<UserListItem, UserAdapter.UserViewHolder>(UserDiffCallback()) {

    private val TAG = "UserAdapter"

    // Cache for user categories and groups to avoid repeated network requests
    private val categoriesCache = mutableMapOf<String, List<String>>()
    private val groupsCache = mutableMapOf<String, List<String>>()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): UserViewHolder {
        val binding = ItemUserBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return UserViewHolder(binding)
    }

    override fun onBindViewHolder(holder: UserViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    /**
     * Submits a new list of users to the adapter.
     * Clears the cache to ensure fresh data is fetched when the list is updated.
     */
    override fun submitList(list: List<UserListItem>?) {
        // Clear caches when a new list is submitted
        categoriesCache.clear()
        groupsCache.clear()
        super.submitList(list)
    }

    /**
     * ViewHolder for a user item.
     */
    inner class UserViewHolder(private val binding: ItemUserBinding) :
        RecyclerView.ViewHolder(binding.root) {

        private val sessionManager = SessionManager(binding.root.context)

        /**
         * Binds a user to the ViewHolder.
         *
         * @param user The user to bind.
         */
        fun bind(user: UserListItem) {
            binding.apply {
                // Display basic user information with labels
                tvUsername.text = binding.root.context.getString(
                    R.string.users__username_label, user.username
                )

                val fullName = "${user.firstName} ${user.lastName}".trim()
                tvFullName.text = binding.root.context.getString(
                    R.string.users__fullname_label,
                    if (fullName.isNotBlank()) fullName else binding.root.context.getString(R.string.not_specified)
                )

                tvEmail.text = binding.root.context.getString(
                    R.string.users__email_label,
                    if (user.email.isNotBlank()) user.email else binding.root.context.getString(R.string.not_specified)
                )

                tvCompany.text = binding.root.context.getString(
                    R.string.users__company_label,
                    user.company ?: binding.root.context.getString(R.string.not_specified)
                )

                tvPosition.text = binding.root.context.getString(
                    R.string.users__position_label,
                    user.position ?: binding.root.context.getString(R.string.not_specified)
                )

                tvDescription.text = binding.root.context.getString(
                    R.string.users__description_label,
                    user.description ?: binding.root.context.getString(R.string.not_specified)
                )

                tvGender.text = binding.root.context.getString(
                    R.string.users__gender_label, user.gender
                )

                tvIsSuperuser.text = binding.root.context.getString(
                    R.string.users__is_superuser_label,
                    if (user.isSuperuser) binding.root.context.getString(R.string.yes) else binding.root.context.getString(R.string.no)
                )

                tvIsLimitedAdmin.text = binding.root.context.getString(
                    R.string.users__is_limited_admin_label,
                    if (user.isLimitedAdmin) binding.root.context.getString(R.string.yes) else binding.root.context.getString(R.string.no)
                )

                tvShortUuid.text = binding.root.context.getString(
                    R.string.users__short_uuid_label, user.shortUuid
                )

                // Fetch and display categories and groups
                fetchUserCategories(user.shortUuid)
                fetchUserGroups(user.shortUuid)
            }
        }

        /**
         * Fetches and displays the categories for a user.
         *
         * @param shortUuid The short UUID of the user.
         */
        private fun fetchUserCategories(shortUuid: String) {
            val token = sessionManager.getAuthToken() ?: return

            // Check if we already have cached data for this user
            if (categoriesCache.containsKey(shortUuid)) {
                // Use cached data
                val categories = categoriesCache[shortUuid] ?: emptyList()
                val categoriesText = if (categories.isNotEmpty()) {
                    categories.joinToString(", ")
                } else {
                    binding.root.context.getString(R.string.none)
                }
                binding.tvCategories.text = binding.root.context.getString(
                    R.string.users__category_label, categoriesText
                )
                return
            }

            // If not in cache, fetch from network
            lifecycleOwner.lifecycleScope.launch {
                try {
                    val result = withContext(Dispatchers.IO) {
                        viewModel.getUserCategories(token, shortUuid)
                    }

                    result.fold(
                        onSuccess = { categories ->
                            // Cache the result
                            categoriesCache[shortUuid] = categories

                            val categoriesText = if (categories.isNotEmpty()) {
                                categories.joinToString(", ")
                            } else {
                                binding.root.context.getString(R.string.none)
                            }
                            binding.tvCategories.text = binding.root.context.getString(
                                R.string.users__category_label, categoriesText
                            )
                        },
                        onFailure = { exception ->
                            Log.e(TAG, "Error fetching user categories", exception)
                            binding.tvCategories.text = binding.root.context.getString(
                                R.string.users__category_label, binding.root.context.getString(R.string.error_loading)
                            )
                        }
                    )
                } catch (e: Exception) {
                    Log.e(TAG, "Exception fetching user categories", e)
                    binding.tvCategories.text = binding.root.context.getString(
                        R.string.users__category_label, binding.root.context.getString(R.string.error_loading)
                    )
                }
            }
        }

        /**
         * Fetches and displays the groups for a user.
         *
         * @param shortUuid The short UUID of the user.
         */
        private fun fetchUserGroups(shortUuid: String) {
            val token = sessionManager.getAuthToken() ?: return

            // Check if we already have cached data for this user
            if (groupsCache.containsKey(shortUuid)) {
                // Use cached data
                val groups = groupsCache[shortUuid] ?: emptyList()
                val groupsText = if (groups.isNotEmpty()) {
                    groups.joinToString(", ")
                } else {
                    binding.root.context.getString(R.string.none)
                }
                binding.tvGroups.text = binding.root.context.getString(
                    R.string.users__group_label, groupsText
                )
                return
            }

            // If not in cache, fetch from network
            lifecycleOwner.lifecycleScope.launch {
                try {
                    val result = withContext(Dispatchers.IO) {
                        viewModel.getUserGroups(token, shortUuid)
                    }

                    result.fold(
                        onSuccess = { groups ->
                            // Cache the result
                            groupsCache[shortUuid] = groups

                            val groupsText = if (groups.isNotEmpty()) {
                                groups.joinToString(", ")
                            } else {
                                binding.root.context.getString(R.string.none)
                            }
                            binding.tvGroups.text = binding.root.context.getString(
                                R.string.users__group_label, groupsText
                            )
                        },
                        onFailure = { exception ->
                            Log.e(TAG, "Error fetching user groups", exception)
                            binding.tvGroups.text = binding.root.context.getString(
                                R.string.users__group_label, binding.root.context.getString(R.string.error_loading)
                            )
                        }
                    )
                } catch (e: Exception) {
                    Log.e(TAG, "Exception fetching user groups", e)
                    binding.tvGroups.text = binding.root.context.getString(
                        R.string.users__group_label, binding.root.context.getString(R.string.error_loading)
                    )
                }
            }
        }
    }

    /**
     * DiffUtil callback for comparing users.
     */
    private class UserDiffCallback : DiffUtil.ItemCallback<UserListItem>() {
        override fun areItemsTheSame(oldItem: UserListItem, newItem: UserListItem): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: UserListItem, newItem: UserListItem): Boolean {
            return oldItem == newItem
        }
    }
}
