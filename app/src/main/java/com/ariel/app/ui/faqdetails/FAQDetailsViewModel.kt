package com.ariel.app.ui.faqdetails

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ariel.app.data.model.FAQ
import com.ariel.app.data.repository.FAQRepository
import kotlinx.coroutines.launch

/**
 * ViewModel for the FAQ details screen that handles FAQ detail operations.
 */
class FAQDetailsViewModel : ViewModel() {

    private val faqRepository = FAQRepository()

    private val _faqState = MutableLiveData<FAQState>()
    val faqState: LiveData<FAQState> = _faqState

    private val _deleteState = MutableLiveData<DeleteState>()
    val deleteState: LiveData<DeleteState> = _deleteState

    private val _currentFAQ = MutableLiveData<FAQ>()
    val currentFAQ: LiveData<FAQ> = _currentFAQ

    /**
     * Fetches the details of a specific FAQ item by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the FAQ item.
     */
    fun fetchFAQDetails(token: String, shortUuid: String) {
        _faqState.value = FAQState.Loading

        viewModelScope.launch {
            try {
                val result = faqRepository.getFAQDetails(token, shortUuid)

                result.fold(
                    onSuccess = { faq ->
                        _faqState.value = FAQState.Success(faq)
                        _currentFAQ.value = faq
                    },
                    onFailure = { exception ->
                        _faqState.value = FAQState.Error(exception.message ?: "Unknown error occurred")
                    }
                )
            } catch (e: Exception) {
                _faqState.value = FAQState.Error(e.message ?: "Unknown error occurred")
            }
        }
    }

    /**
     * Deletes a FAQ item by its short UUID.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the FAQ item to delete.
     */
    fun deleteFAQ(token: String, shortUuid: String) {
        _deleteState.value = DeleteState.Loading

        viewModelScope.launch {
            try {
                val result = faqRepository.deleteFAQ(token, shortUuid)

                result.fold(
                    onSuccess = {
                        _deleteState.value = DeleteState.Success
                    },
                    onFailure = { exception ->
                        _deleteState.value = DeleteState.Error(exception.message ?: "Unknown error occurred")
                    }
                )
            } catch (e: Exception) {
                _deleteState.value = DeleteState.Error(e.message ?: "Unknown error occurred")
            }
        }
    }

    /**
     * Sealed class representing the state of the FAQ details.
     */
    sealed class FAQState {
        /**
         * Represents the loading state.
         */
        object Loading : FAQState()

        /**
         * Represents a successful state with FAQ details.
         *
         * @property faq The FAQ details.
         */
        data class Success(val faq: FAQ) : FAQState()

        /**
         * Represents an error state.
         *
         * @property errorMessage The error message.
         */
        data class Error(val errorMessage: String) : FAQState()
    }

    /**
     * Sealed class representing the state of the delete operation.
     */
    sealed class DeleteState {
        /**
         * Represents the loading state.
         */
        object Loading : DeleteState()

        /**
         * Represents a successful delete operation.
         */
        object Success : DeleteState()

        /**
         * Represents an error state.
         *
         * @property errorMessage The error message.
         */
        data class Error(val errorMessage: String) : DeleteState()
    }
}
