package com.ariel.app.ui.newfaq

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ariel.app.data.model.FAQ
import com.ariel.app.data.repository.FAQRepository
import kotlinx.coroutines.launch

/**
 * ViewModel for the new FAQ screen.
 */
class NewFAQViewModel : ViewModel() {

    private val faqRepository = FAQRepository()

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error

    private val _faqCreationResult = MutableLiveData<FAQCreationResult>()
    val faqCreationResult: LiveData<FAQCreationResult> = _faqCreationResult

    /**
     * Creates a new FAQ item.
     *
     * @param token The authentication token.
     * @param title The title of the FAQ item.
     * @param body The content body of the FAQ item.
     */
    fun createFAQ(token: String, title: String, body: String) {
        _isLoading.value = true
        _error.value = null

        viewModelScope.launch {
            try {
                val result = faqRepository.createFAQ(token, title, body)

                result.fold(
                    onSuccess = { faq ->
                        _faqCreationResult.value = FAQCreationResult.Success(faq)
                    },
                    onFailure = { exception ->
                        _faqCreationResult.value = FAQCreationResult.Error(
                            exception.message ?: "Failed to create FAQ"
                        )
                    }
                )
            } catch (e: Exception) {
                _faqCreationResult.value = FAQCreationResult.Error(
                    e.message ?: "Unknown error occurred"
                )
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Validates the input fields for creating a new FAQ item.
     *
     * @param title The title of the FAQ item.
     * @param body The content body of the FAQ item.
     * @return A NewFAQValidationResult indicating whether the inputs are valid.
     */
    fun validateInputs(title: String, body: String): NewFAQValidationResult {
        val isTitleValid = title.isNotBlank()
        val isBodyValid = body.isNotBlank()

        return NewFAQValidationResult(isTitleValid, isBodyValid)
    }

    /**
     * Clears any error messages.
     */
    fun clearError() {
        _error.value = null
    }

    /**
     * Sealed class representing the result of creating a new FAQ item.
     */
    sealed class FAQCreationResult {
        data class Success(val faq: FAQ) : FAQCreationResult()
        data class Error(val errorMessage: String) : FAQCreationResult()
    }
}

/**
 * Data class representing the validation result for a new FAQ item.
 *
 * @property isTitleValid Whether the title is valid.
 * @property isBodyValid Whether the body is valid.
 */
data class NewFAQValidationResult(
    val isTitleValid: Boolean,
    val isBodyValid: Boolean
)
