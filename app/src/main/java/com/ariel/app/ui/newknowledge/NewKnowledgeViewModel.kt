package com.ariel.app.ui.newknowledge

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ariel.app.data.model.Knowledge
import com.ariel.app.data.repository.KnowledgeRepository
import kotlinx.coroutines.launch

/**
 * ViewModel for the new knowledge screen.
 */
class NewKnowledgeViewModel : ViewModel() {

    private val knowledgeRepository = KnowledgeRepository()

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error

    private val _knowledgeCreationResult = MutableLiveData<KnowledgeCreationResult>()
    val knowledgeCreationResult: LiveData<KnowledgeCreationResult> = _knowledgeCreationResult

    /**
     * Creates a new knowledge item.
     *
     * @param token The authentication token.
     * @param title The title of the knowledge item.
     * @param body The content body of the knowledge item.
     */
    fun createKnowledge(token: String, title: String, body: String) {
        _isLoading.value = true
        _error.value = null

        viewModelScope.launch {
            try {
                val result = knowledgeRepository.createKnowledge(token, title, body)

                result.fold(
                    onSuccess = { knowledge ->
                        _knowledgeCreationResult.value = KnowledgeCreationResult.Success(knowledge)
                    },
                    onFailure = { exception ->
                        _knowledgeCreationResult.value = KnowledgeCreationResult.Error(
                            exception.message ?: "Failed to create knowledge"
                        )
                    }
                )
            } catch (e: Exception) {
                _knowledgeCreationResult.value = KnowledgeCreationResult.Error(
                    e.message ?: "Unknown error occurred"
                )
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Validates the input fields for creating a new knowledge item.
     *
     * @param title The title of the knowledge item.
     * @param body The content body of the knowledge item.
     * @return A NewKnowledgeValidationResult indicating whether the inputs are valid.
     */
    fun validateInputs(title: String, body: String): NewKnowledgeValidationResult {
        val isTitleValid = title.isNotBlank()
        val isBodyValid = body.isNotBlank()

        return NewKnowledgeValidationResult(isTitleValid, isBodyValid)
    }

    /**
     * Clears any error messages.
     */
    fun clearError() {
        _error.value = null
    }

    /**
     * Sealed class representing the result of creating a new knowledge item.
     */
    sealed class KnowledgeCreationResult {
        data class Success(val knowledge: Knowledge) : KnowledgeCreationResult()
        data class Error(val errorMessage: String) : KnowledgeCreationResult()
    }
}

/**
 * Data class representing the validation result for a new knowledge item.
 *
 * @property isTitleValid Whether the title is valid.
 * @property isBodyValid Whether the body is valid.
 */
data class NewKnowledgeValidationResult(
    val isTitleValid: Boolean,
    val isBodyValid: Boolean
)
