package com.ariel.app.ui.home

import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import com.google.android.material.card.MaterialCardView
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.ariel.app.ArielApplication
import com.ariel.app.R
import com.ariel.app.data.model.TicketStatistics
import com.ariel.app.data.model.EventGroup
import com.ariel.app.databinding.FragmentHomeBinding
import java.text.NumberFormat
import java.util.Locale

class HomeFragment : Fragment() {

    private var _binding: FragmentHomeBinding? = null
    private lateinit var homeViewModel: HomeViewModel

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val sessionManager = (requireActivity().application as ArielApplication).sessionManager
        val factory = HomeViewModelFactory(sessionManager)
        homeViewModel = ViewModelProvider(this, factory)[HomeViewModel::class.java]

        _binding = FragmentHomeBinding.inflate(inflater, container, false)
        val root: View = binding.root

        setupUI()
        setupObservers()

        return root
    }

    override fun onResume() {
        super.onResume()
        // refresh homepage data every time user navigates back to homepage
        homeViewModel.refreshHomepageData()
    }

    private fun setupUI() {
        // Setup "All Tickets" box click listener
        binding.boxAllTickets.setOnClickListener {
            navigateToTicketsList()
        }
    }

    private fun setupObservers() {
        homeViewModel.ticketStatistics.observe(viewLifecycleOwner) { statistics ->
            createTicketStatisticsBoxes(statistics)
        }

        homeViewModel.groups.observe(viewLifecycleOwner) { groups ->
            createGroupBoxes(groups)
        }

        homeViewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            if (!isLoading) {
                binding.errorMessage.visibility = View.GONE
            }
        }
    }

    private fun createTicketStatisticsBoxes(statistics: List<TicketStatistics>) {
        // Clear existing ticket statistics boxes (keep groups)
        clearTicketStatisticsBoxes()

        if (statistics.isEmpty()) {
            binding.errorMessage.text = "اطلاعات در دسترس نیست"
            binding.errorMessage.visibility = View.VISIBLE
            return
        }

        // Create boxes in rows of 2
        var currentRowContainer: LinearLayout? = null

        statistics.forEachIndexed { index, stat ->
            // Create new row container if needed
            if (index % 2 == 0) {
                currentRowContainer = LinearLayout(requireContext()).apply {
                    orientation = LinearLayout.HORIZONTAL
                    layoutParams = LinearLayout.LayoutParams(
                        LinearLayout.LayoutParams.MATCH_PARENT,
                        LinearLayout.LayoutParams.WRAP_CONTENT
                    ).apply {
                        setMargins(0, 8, 0, 8)
                    }
                }
                binding.containerDynamicBoxes.addView(currentRowContainer)
            }

            val box = createTicketStatisticsBox(stat)

            val layoutParams = LinearLayout.LayoutParams(
                0,
                resources.getDimensionPixelSize(R.dimen.box_height),
                1f
            ).apply {
                if (index % 2 == 0) {
                    setMargins(0, 0, 4, 0)
                } else {
                    setMargins(4, 0, 0, 0)
                }
            }
            box.layoutParams = layoutParams
            currentRowContainer?.addView(box)
        }

        binding.errorMessage.visibility = View.GONE
    }

    private fun createTicketStatisticsBox(statistics: TicketStatistics): MaterialCardView {
        val cardView = MaterialCardView(requireContext()).apply {
            cardElevation = 0f
            radius = resources.getDimension(R.dimen.card_corner_radius)
            strokeWidth = resources.getDimensionPixelSize(R.dimen.card_stroke_width)
            strokeColor = ContextCompat.getColor(requireContext(), R.color.card_border)
        }

        val container = LinearLayout(requireContext()).apply {
            orientation = LinearLayout.VERTICAL
            gravity = android.view.Gravity.CENTER
            setPadding(16, 16, 16, 16)
            isClickable = true
            isFocusable = true
            background = ContextCompat.getDrawable(requireContext(), android.R.attr.selectableItemBackground)
        }

        val labelView = TextView(requireContext()).apply {
            text = statistics.label
            textSize = 12f
            setTypeface(null, android.graphics.Typeface.BOLD)
            gravity = android.view.Gravity.CENTER
            setTextColor(ContextCompat.getColor(requireContext(), R.color.statistics_text_color))
        }

        val countView = TextView(requireContext()).apply {
            text = convertToPersianNumerals(statistics.count.toString())
            textSize = 18f
            setTypeface(null, android.graphics.Typeface.BOLD)
            gravity = android.view.Gravity.CENTER
        }

        // Set colors based on value
        val colors = getColorsForValue(statistics.value)
        countView.setTextColor(colors.first)
        cardView.setCardBackgroundColor(colors.second)

        container.addView(labelView)
        container.addView(countView)
        cardView.addView(container)

        // Set click listener
        container.setOnClickListener {
            navigateToTicketsListWithStatus(statistics.value)
        }

        return cardView
    }

    private fun getColorsForValue(value: String): Pair<Int, Int> {
        return when (value) {
            "P" -> Pair(
                ContextCompat.getColor(requireContext(), R.color.homepage_pending_fg),
                ContextCompat.getColor(requireContext(), R.color.homepage_pending_bg)
            )
            "I" -> Pair(
                ContextCompat.getColor(requireContext(), R.color.homepage_inprogress_fg),
                ContextCompat.getColor(requireContext(), R.color.homepage_inprogress_bg)
            )
            "R" -> Pair(
                ContextCompat.getColor(requireContext(), R.color.homepage_resolved_fg),
                ContextCompat.getColor(requireContext(), R.color.homepage_resolved_bg)
            )
            "unvisited" -> Pair(
                ContextCompat.getColor(requireContext(), R.color.homepage_unvisited_fg),
                ContextCompat.getColor(requireContext(), R.color.homepage_unvisited_bg)
            )
            else -> Pair(
                ContextCompat.getColor(requireContext(), R.color.statistics_text_color),
                ContextCompat.getColor(requireContext(), R.color.surface)
            )
        }
    }

    private fun createGroupBoxes(groups: List<EventGroup>) {
        if (groups.isEmpty()) return

        // Create boxes in rows of 2
        var currentRowContainer: LinearLayout? = null

        groups.forEachIndexed { index, group ->
            // Create new row container if needed
            if (index % 2 == 0) {
                currentRowContainer = LinearLayout(requireContext()).apply {
                    orientation = LinearLayout.HORIZONTAL
                    layoutParams = LinearLayout.LayoutParams(
                        LinearLayout.LayoutParams.MATCH_PARENT,
                        LinearLayout.LayoutParams.WRAP_CONTENT
                    ).apply {
                        setMargins(0, 16, 0, 8)
                    }
                }
                binding.containerDynamicBoxes.addView(currentRowContainer)
            }

            val box = createGroupBox(group)

            val layoutParams = LinearLayout.LayoutParams(
                0,
                resources.getDimensionPixelSize(R.dimen.box_height),
                1f
            ).apply {
                if (index % 2 == 0) {
                    setMargins(0, 0, 4, 0)
                } else {
                    setMargins(4, 0, 0, 0)
                }
            }
            box.layoutParams = layoutParams
            currentRowContainer?.addView(box)
        }
    }

    private fun createGroupBox(group: EventGroup): MaterialCardView {
        val cardView = MaterialCardView(requireContext()).apply {
            cardElevation = 0f
            radius = resources.getDimension(R.dimen.card_corner_radius)
            strokeWidth = resources.getDimensionPixelSize(R.dimen.card_stroke_width)
            strokeColor = ContextCompat.getColor(requireContext(), R.color.card_border)
            setCardBackgroundColor(ContextCompat.getColor(requireContext(), R.color.homepage_group_bg))
        }

        val container = LinearLayout(requireContext()).apply {
            orientation = LinearLayout.VERTICAL
            gravity = android.view.Gravity.CENTER
            setPadding(16, 16, 16, 16)
            isClickable = true
            isFocusable = true
            background = ContextCompat.getDrawable(requireContext(), android.R.attr.selectableItemBackground)
        }

        val labelView = TextView(requireContext()).apply {
            text = group.name
            textSize = 12f
            setTypeface(null, android.graphics.Typeface.BOLD)
            gravity = android.view.Gravity.CENTER
            setTextColor(ContextCompat.getColor(requireContext(), R.color.homepage_group_fg))
            maxLines = 2
        }

        container.addView(labelView)
        cardView.addView(container)

        // Set click listener
        container.setOnClickListener {
            navigateToTicketsListWithGroup(group.id)
        }

        return cardView
    }

    private fun clearTicketStatisticsBoxes() {
        binding.containerDynamicBoxes.removeAllViews()
    }

    private fun navigateToTicketsList() {
        try {
            findNavController().navigate(R.id.action_nav_home_to_nav_tickets)
        } catch (e: Exception) {
            Log.e("HomeFragment", "Navigation error", e)
        }
    }

    private fun navigateToTicketsListWithStatus(status: String) {
        try {
            val bundle = android.os.Bundle().apply {
                putString("status", status)
            }
            findNavController().navigate(R.id.action_nav_home_to_nav_tickets, bundle)
        } catch (e: Exception) {
            Log.e("HomeFragment", "Navigation error", e)
        }
    }

    private fun navigateToTicketsListWithGroup(groupId: Int) {
        try {
            val bundle = android.os.Bundle().apply {
                putInt("group-id", groupId)
            }
            findNavController().navigate(R.id.action_nav_home_to_nav_tickets, bundle)
        } catch (e: Exception) {
            Log.e("HomeFragment", "Navigation error", e)
        }
    }

    private fun convertToPersianNumerals(input: String): String {
        val persianDigits = arrayOf('۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹')
        val englishDigits = arrayOf('0', '1', '2', '3', '4', '5', '6', '7', '8', '9')

        var result = input
        for (i in englishDigits.indices) {
            result = result.replace(englishDigits[i], persianDigits[i])
        }
        return result
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
