package com.ariel.app.ui.events

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ariel.app.data.model.Event
import com.ariel.app.data.repository.EventRepository
import kotlinx.coroutines.launch

/**
 * ViewModel for the events screen that handles event data operations with pagination.
 */
class EventsViewModel : ViewModel() {

    private val eventRepository = EventRepository()

    private val _events = MutableLiveData<List<Event>>()
    val events: LiveData<List<Event>> = _events

    private val _isLoading = MutableLiveData<Boolean>(false)
    val isLoading: LiveData<Boolean> = _isLoading

    private val _isLoadingMore = MutableLiveData<Boolean>(false)
    val isLoadingMore: LiveData<Boolean> = _isLoadingMore

    private val _error = MutableLiveData<String?>(null)
    val error: LiveData<String?> = _error

    private var nextPageUrl: String? = null
    private var currentEvents = mutableListOf<Event>()

    /**
     * Fetches the first page of events.
     *
     * @param token The authentication token.
     */
    fun fetchEvents(token: String) {
        _isLoading.value = true
        _error.value = null
        currentEvents.clear()

        viewModelScope.launch {
            try {
                val result = eventRepository.getEvents(token, 15)

                result.fold(
                    onSuccess = { paginatedResponse ->
                        currentEvents.addAll(paginatedResponse.results)
                        _events.value = currentEvents.toList()
                        nextPageUrl = paginatedResponse.next
                    },
                    onFailure = { exception ->
                        _error.value = exception.message ?: "Unknown error occurred"
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message ?: "Unknown error occurred"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Loads more events from the next page if available.
     *
     * @param token The authentication token.
     */
    fun loadMoreEvents(token: String) {
        if (nextPageUrl == null || _isLoadingMore.value == true) {
            return
        }

        _isLoadingMore.value = true

        viewModelScope.launch {
            try {
                val result = eventRepository.getEventsFromUrl(token, nextPageUrl!!)

                result.fold(
                    onSuccess = { paginatedResponse ->
                        currentEvents.addAll(paginatedResponse.results)
                        _events.value = currentEvents.toList()
                        nextPageUrl = paginatedResponse.next
                    },
                    onFailure = { exception ->
                        _error.value = exception.message ?: "Unknown error occurred"
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message ?: "Unknown error occurred"
            } finally {
                _isLoadingMore.value = false
            }
        }
    }

    /**
     * Checks if there are more pages to load.
     *
     * @return True if there are more pages, false otherwise.
     */
    fun hasMorePages(): Boolean {
        return nextPageUrl != null
    }

    /**
     * Clears any error messages.
     */
    fun clearError() {
        _error.value = null
    }
}
