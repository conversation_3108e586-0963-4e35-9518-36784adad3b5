package com.ariel.app.ui.eventdetails

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.Html
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import android.widget.Toast

import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.ariel.app.R
import com.ariel.app.data.SessionManager
import com.ariel.app.data.model.Event
import com.ariel.app.databinding.FragmentEventDetailsBinding
import com.ariel.app.utils.HtmlRenderer

/**
 * Fragment for displaying the details of an event item.
 */
class EventDetailsFragment : Fragment() {

    private var _binding: FragmentEventDetailsBinding? = null
    private val binding get() = _binding!!

    private lateinit var viewModel: EventDetailsViewModel
    private lateinit var sessionManager: SessionManager
    private var shortUuid: String? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        viewModel = ViewModelProvider(this).get(EventDetailsViewModel::class.java)
        _binding = FragmentEventDetailsBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sessionManager = SessionManager(requireContext())
        shortUuid = arguments?.getString("shortUuid")

        observeViewModel()
        fetchEventDetails()
        setupDeleteButton()
        setupEditButton()
    }

    /**
     * Observes changes in the ViewModel's LiveData.
     */
    private fun observeViewModel() {
        viewModel.eventState.observe(viewLifecycleOwner) { state ->
            when (state) {
                is EventDetailsViewModel.EventState.Loading -> {
                    showLoading()
                }
                is EventDetailsViewModel.EventState.Success -> {
                    hideLoading()
                    displayEvent(state.event)
                }
                is EventDetailsViewModel.EventState.Error -> {
                    hideLoading()
                    showError(state.errorMessage)
                }
            }
        }

        viewModel.deleteState.observe(viewLifecycleOwner) { state ->
            when (state) {
                is EventDetailsViewModel.DeleteState.Loading -> {
                    showLoading()
                }
                is EventDetailsViewModel.DeleteState.Success -> {
                    hideLoading()
                    Toast.makeText(requireContext(), getString(R.string.event_deleted_successfully), Toast.LENGTH_SHORT).show()
                    // Navigate to home after deletion
                    findNavController().navigate(R.id.nav_home)
                }
                is EventDetailsViewModel.DeleteState.Error -> {
                    hideLoading()
                    Toast.makeText(requireContext(), state.errorMessage, Toast.LENGTH_LONG).show()
                }
            }
        }
    }

    /**
     * Displays the event details in the UI.
     *
     * @param event The event item to display.
     */
    private fun displayEvent(event: Event) {
        binding.apply {
            cardEvent.visibility = View.VISIBLE
            tvEventTitle.text = event.title
            tvEventGroup.text = event.group.name
            tvEventDate.text = event.createdJalali
            tvEventId.text = getString(R.string.id_format, event.shortUuid)
            HtmlRenderer.applyToTextView(tvEventBody, event.body)

            // Handle file attachment if present
            if (event.hasFile && event.file != null) {
                layoutFileAttachment.visibility = View.VISIBLE
                tvEventFileName.text = event.file.substringAfterLast('/')
                btnDownloadFile.setOnClickListener {
                    openFileInBrowser(event.file)
                }
            } else {
                layoutFileAttachment.visibility = View.GONE
            }

            // Show edit and delete buttons only for superusers
            if (sessionManager.getUserIsSuperuser()) {
                btnEditEvent.visibility = View.VISIBLE
                btnDeleteEvent.visibility = View.VISIBLE
            } else {
                btnEditEvent.visibility = View.GONE
                btnDeleteEvent.visibility = View.GONE
            }
        }
    }

    /**
     * Opens the file URL in a browser.
     *
     * @param fileUrl The URL of the file to open.
     */
    private fun openFileInBrowser(fileUrl: String) {
        try {
            val intent = Intent(Intent.ACTION_VIEW, Uri.parse(fileUrl))
            startActivity(intent)
        } catch (e: Exception) {
            Toast.makeText(
                requireContext(),
                getString(R.string.error_opening_file),
                Toast.LENGTH_SHORT
            ).show()
        }
    }

    /**
     * Shows the loading indicator and hides other views.
     */
    private fun showLoading() {
        binding.progressBar.visibility = View.VISIBLE
        binding.cardEvent.visibility = View.GONE
        binding.tvError.visibility = View.GONE
    }

    /**
     * Hides the loading indicator.
     */
    private fun hideLoading() {
        binding.progressBar.visibility = View.GONE
    }

    /**
     * Shows an error message.
     *
     * @param message The error message to display.
     */
    private fun showError(message: String) {
        binding.tvError.text = message
        binding.tvError.visibility = View.VISIBLE
        binding.cardEvent.visibility = View.GONE
    }

    /**
     * Fetches event details from the API.
     */
    private fun fetchEventDetails() {
        val token = sessionManager.getAuthToken()
        if (token != null && shortUuid != null) {
            viewModel.fetchEventDetails(token, shortUuid!!)
        } else if (token == null) {
            showError(getString(R.string.authentication_token_not_found))
        } else {
            showError(getString(R.string.event_id_not_found))
        }
    }

    /**
     * Sets up the delete button click listener.
     */
    private fun setupDeleteButton() {
        binding.btnDeleteEvent.setOnClickListener {
            showDeleteConfirmationDialog()
        }
    }

    /**
     * Shows a confirmation dialog before deleting the event item.
     */
    private fun showDeleteConfirmationDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_delete_confirm, null)
        val titleTextView = dialogView.findViewById<TextView>(R.id.tv_dialog_title)
        val messageTextView = dialogView.findViewById<TextView>(R.id.tv_dialog_message)
        val confirmButton = dialogView.findViewById<Button>(R.id.btn_confirm)
        val cancelButton = dialogView.findViewById<Button>(R.id.btn_cancel)

        // Set the title and message
        titleTextView.text = getString(R.string.delete_event_confirmation_title)
        messageTextView.text = getString(R.string.delete_event_confirmation_message)

        val dialog = AlertDialog.Builder(requireContext())
            .setView(dialogView)
            .setCancelable(true)
            .create()

        // Set up button click listeners
        confirmButton.setOnClickListener {
            deleteEvent()
            dialog.dismiss()
        }

        cancelButton.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }

    /**
     * Deletes the event item.
     */
    private fun deleteEvent() {
        val token = sessionManager.getAuthToken()
        if (token != null && shortUuid != null) {
            viewModel.deleteEvent(token, shortUuid!!)
        } else if (token == null) {
            Toast.makeText(requireContext(), getString(R.string.authentication_token_not_found), Toast.LENGTH_SHORT).show()
        } else {
            Toast.makeText(requireContext(), getString(R.string.event_id_not_found), Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * Sets up the edit button click listener.
     */
    private fun setupEditButton() {
        binding.btnEditEvent.setOnClickListener {
            navigateToEditEvent()
        }
    }

    /**
     * Navigates to the edit event screen.
     */
    private fun navigateToEditEvent() {
        val currentEvent = viewModel.currentEvent.value
        if (currentEvent != null && shortUuid != null) {
            val bundle = Bundle().apply {
                putString("shortUuid", shortUuid)
                putParcelable("event", currentEvent)
            }
            findNavController().navigate(
                R.id.action_eventDetailsFragment_to_editEventFragment,
                bundle
            )
        } else {
            Toast.makeText(requireContext(), getString(R.string.event_data_not_found), Toast.LENGTH_SHORT).show()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
