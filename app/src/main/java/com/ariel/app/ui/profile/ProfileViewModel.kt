package com.ariel.app.ui.profile

import android.util.Log
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ariel.app.data.model.Profile
import com.ariel.app.data.repository.ProfileRepository
import kotlinx.coroutines.launch

/**
 * ViewModel for the profile screen that handles profile data operations.
 */
class ProfileViewModel : ViewModel() {

    private val profileRepository = ProfileRepository()
    private val TAG = "ProfileViewModel"

    private val _profileState = MutableLiveData<ProfileState>()
    val profileState: LiveData<ProfileState> = _profileState

    private val _passwordChangeState = MutableLiveData<PasswordChangeState?>()
    val passwordChangeState: LiveData<PasswordChangeState?> = _passwordChangeState

    /**
     * Fetches the profile information for the authenticated user.
     *
     * @param token The authentication token.
     */
    fun fetchProfile(token: String) {
        _profileState.value = ProfileState.Loading

        viewModelScope.launch {
            val result = profileRepository.getProfile(token)

            result.fold(
                onSuccess = { profile ->
                    _profileState.value = ProfileState.Success(profile)
                },
                onFailure = { error ->
                    _profileState.value = ProfileState.Error(error.message ?: "Unknown error")
                }
            )
        }
    }

    /**
     * Changes the password for the authenticated user.
     *
     * @param token The authentication token.
     * @param oldPassword The current password of the user.
     * @param newPassword1 The new password.
     * @param newPassword2 The confirmation of the new password.
     */
    fun changePassword(
        token: String,
        oldPassword: String,
        newPassword1: String,
        newPassword2: String
    ) {
        // Validate inputs
        if (oldPassword.isBlank() || newPassword1.isBlank() || newPassword2.isBlank()) {
            _passwordChangeState.value = PasswordChangeState.Error("All fields are required")
            return
        }

        if (newPassword1 != newPassword2) {
            _passwordChangeState.value = PasswordChangeState.Error("New passwords do not match")
            return
        }

        if (newPassword1.length < 8) {
            _passwordChangeState.value = PasswordChangeState.Error("Password must be at least 8 characters long")
            return
        }

        // Set loading state
        _passwordChangeState.value = PasswordChangeState.Loading

        // Make API call
        viewModelScope.launch {
            Log.d(TAG, "Changing password")
            val result = profileRepository.changePassword(
                token,
                oldPassword,
                newPassword1,
                newPassword2
            )

            result.fold(
                onSuccess = {
                    Log.d(TAG, "Password changed successfully")
                    _passwordChangeState.value = PasswordChangeState.Success
                },
                onFailure = { error ->
                    Log.e(TAG, "Error changing password", error)
                    _passwordChangeState.value = PasswordChangeState.Error(error.message ?: "Unknown error")
                }
            )
        }
    }

    /**
     * Resets the password change state.
     */
    fun resetPasswordChangeState() {
        _passwordChangeState.value = null
    }

    /**
     * Sealed class representing the different states of the profile screen.
     */
    sealed class ProfileState {
        /**
         * Loading state when the profile data is being fetched.
         */
        object Loading : ProfileState()

        /**
         * Success state when the profile data has been successfully fetched.
         *
         * @property profile The fetched profile data.
         */
        data class Success(val profile: Profile) : ProfileState()

        /**
         * Error state when there was an error fetching the profile data.
         *
         * @property message The error message.
         */
        data class Error(val message: String) : ProfileState()
    }

    /**
     * Sealed class representing the different states of the password change operation.
     */
    sealed class PasswordChangeState {
        /**
         * Loading state when the password change request is being processed.
         */
        object Loading : PasswordChangeState()

        /**
         * Success state when the password has been successfully changed.
         */
        object Success : PasswordChangeState()

        /**
         * Error state when there was an error changing the password.
         *
         * @property message The error message.
         */
        data class Error(val message: String) : PasswordChangeState()
    }
}
