package com.ariel.app

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.ProgressBar
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AlertDialog
import androidx.core.os.bundleOf
import androidx.navigation.NavController
import androidx.navigation.NavDestination
import androidx.navigation.findNavController
import androidx.navigation.ui.AppBarConfiguration
import androidx.navigation.ui.navigateUp
import androidx.navigation.ui.setupActionBarWithNavController
import androidx.appcompat.app.AppCompatActivity
import com.ariel.app.data.SessionManager
import com.ariel.app.databinding.ActivityMainBinding
import com.ariel.app.ui.login.LoginActivity
import com.google.android.material.dialog.MaterialAlertDialogBuilder

class MainActivity : AppCompatActivity() {

    private lateinit var appBarConfiguration: AppBarConfiguration
    private lateinit var binding: ActivityMainBinding
    private lateinit var sessionManager: SessionManager
    private lateinit var navController: NavController

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Initialize session manager
        sessionManager = SessionManager(this)

        // Check if user is logged in, if not redirect to login
        if (!sessionManager.isLoggedIn()) {
            redirectToLogin()
            return
        }

        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setSupportActionBar(binding.appBarMain.toolbar)

        // Hide the FAB in the main activity since we're using a FAB in the TicketsFragment
        binding.appBarMain.fab.visibility = View.GONE

        navController = findNavController(R.id.nav_host_fragment_content_main)

        // Include top-level destinations without drawer
        appBarConfiguration = AppBarConfiguration(
            setOf(
                R.id.nav_home, R.id.nav_profile, R.id.nav_tickets, R.id.nav_new_ticket,
                R.id.nav_knowledges, R.id.nav_new_knowledge, R.id.nav_faqs, R.id.nav_new_faq,
                R.id.nav_events, R.id.nav_new_event, R.id.nav_users
            )
        )
        setupActionBarWithNavController(navController, appBarConfiguration)

        // Set up navigation destination change listener for dynamic button handling
        setupNavigationDestinationListener()

        // Set up back navigation
        setupBackNavigation()
    }

    /**
     * Sets up navigation destination change listener for dynamic button handling.
     */
    private fun setupNavigationDestinationListener() {
        navController.addOnDestinationChangedListener { _, destination, arguments ->
            // Use post to avoid ConcurrentModificationException
            binding.root.post {
                updateAppBarConfiguration(destination)
            }
        }
    }

    /**
     * Updates the AppBarConfiguration based on the current destination.
     */
    private fun updateAppBarConfiguration(destination: NavDestination) {
        // Dynamically update AppBarConfiguration based on navigation scenario
        val topLevelDestinations = mutableSetOf(
            R.id.nav_home, R.id.nav_profile, R.id.nav_tickets, R.id.nav_new_ticket,
            R.id.nav_knowledges, R.id.nav_new_knowledge, R.id.nav_faqs, R.id.nav_new_faq,
            R.id.nav_events, R.id.nav_new_event, R.id.nav_users
        )

        // Check if we should show hamburger menu for detail pages
        when (destination.id) {
            R.id.ticketDetailsFragment -> {
                // Check if we came from new ticket creation
                val backStackEntry = navController.previousBackStackEntry
                if (backStackEntry?.destination?.id == R.id.nav_new_ticket) {
                    // Coming from new ticket creation - add to top level destinations
                    topLevelDestinations.add(R.id.ticketDetailsFragment)
                }
            }
            R.id.eventDetailsFragment -> {
                // Check if we came from new event creation or edit
                val backStackEntry = navController.previousBackStackEntry
                if (backStackEntry?.destination?.id == R.id.nav_new_event ||
                    backStackEntry?.destination?.id == R.id.editEventFragment) {
                    topLevelDestinations.add(R.id.eventDetailsFragment)
                }
            }
            R.id.faqDetailsFragment -> {
                // Check if we came from new FAQ creation or edit
                val backStackEntry = navController.previousBackStackEntry
                if (backStackEntry?.destination?.id == R.id.nav_new_faq ||
                    backStackEntry?.destination?.id == R.id.editFAQFragment) {
                    topLevelDestinations.add(R.id.faqDetailsFragment)
                }
            }
            R.id.knowledgeDetailsFragment -> {
                // Check if we came from new knowledge creation or edit
                val backStackEntry = navController.previousBackStackEntry
                if (backStackEntry?.destination?.id == R.id.nav_new_knowledge ||
                    backStackEntry?.destination?.id == R.id.editKnowledgeFragment) {
                    topLevelDestinations.add(R.id.knowledgeDetailsFragment)
                }
            }
        }

        // Update AppBarConfiguration with new top level destinations
        appBarConfiguration = AppBarConfiguration(topLevelDestinations)
        setupActionBarWithNavController(navController, appBarConfiguration)
    }

    /**
     * Determines whether to show back button based on navigation scenario.
     */
    private fun shouldShowBackButton(destination: NavDestination, arguments: Bundle?): Boolean {
        return when (destination.id) {
            // Detail pages should show back button when coming from list pages
            R.id.ticketDetailsFragment,
            R.id.eventDetailsFragment,
            R.id.faqDetailsFragment,
            R.id.knowledgeDetailsFragment -> {
                // These should show back button when coming from list pages or search
                true
            }
            // Edit pages should show hamburger menu (coming from create/edit operations)
            R.id.editEventFragment,
            R.id.editFAQFragment,
            R.id.editKnowledgeFragment -> {
                // These should show hamburger menu as they come from edit operations
                false
            }
            // Search results should show back button
            R.id.searchResultsFragment -> true
            // All other destinations use default behavior
            else -> false
        }
    }

    /**
     * Sets up back navigation handling.
     */
    private fun setupBackNavigation() {
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                // If we're at a top-level destination, close the app
                // Otherwise, navigate up
                if (navController.currentDestination?.id in setOf(
                        R.id.nav_home, R.id.nav_profile, R.id.nav_tickets, R.id.nav_new_ticket,
                        R.id.nav_knowledges, R.id.nav_new_knowledge, R.id.nav_faqs, R.id.nav_new_faq,
                        R.id.nav_events, R.id.nav_new_event, R.id.nav_users
                    )
                ) {
                    finish()
                } else {
                    navController.navigateUp()
                }
            }
        })
    }



    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        // Inflate the menu; this adds items to the action bar if it is present.
        menuInflater.inflate(R.menu.main, menu)

        // Set up action menu visibility based on user role
        setupActionMenuVisibility(menu)

        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_new_ticket -> {
                // Navigate to new ticket
                navController.navigate(R.id.nav_new_ticket)
                true
            }
            R.id.action_new_knowledge -> {
                // Navigate to new knowledge
                navController.navigate(R.id.nav_new_knowledge)
                true
            }
            R.id.action_new_faq -> {
                // Navigate to new FAQ
                navController.navigate(R.id.nav_new_faq)
                true
            }
            R.id.action_new_event -> {
                // Navigate to new event
                navController.navigate(R.id.nav_new_event)
                true
            }
            R.id.action_home -> {
                // Navigate to home
                navController.navigate(R.id.nav_home)
                true
            }
            R.id.action_search -> {
                // Show search dialog
                showSearchDialog()
                true
            }
            R.id.action_logout -> {
                // Show confirmation dialog before logging out
                showLogoutConfirmationDialog()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    /**
     * Shows a dialog for entering a search query.
     */
    private fun showSearchDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_search, null)
        val searchQueryEditText = dialogView.findViewById<EditText>(R.id.et_search_query)
        val searchButton = dialogView.findViewById<Button>(R.id.btn_search)
        val cancelButton = dialogView.findViewById<Button>(R.id.btn_cancel)
        val progressBar = dialogView.findViewById<ProgressBar>(R.id.progress_bar)

        val dialog = MaterialAlertDialogBuilder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        // Set up button click listeners
        searchButton.setOnClickListener {
            val query = searchQueryEditText.text.toString().trim()
            if (query.isNotEmpty()) {
                // Navigate to search results fragment with the query
                val bundle = bundleOf("query" to query)
                navController.navigate(R.id.searchResultsFragment, bundle)
                dialog.dismiss()
            } else {
                // Show error if query is empty
                searchQueryEditText.error = getString(R.string.search_hint)
            }
        }

        cancelButton.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }

    /**
     * Shows a confirmation dialog before logging out.
     */
    private fun showLogoutConfirmationDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_logout_confirm, null)
        val confirmButton = dialogView.findViewById<Button>(R.id.btn_confirm)
        val cancelButton = dialogView.findViewById<Button>(R.id.btn_cancel)

        val dialog = MaterialAlertDialogBuilder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        // Set up button click listeners
        confirmButton.setOnClickListener {
            // User confirmed logout
            sessionManager.clearSession()
            redirectToLogin()
            dialog.dismiss()
        }

        cancelButton.setOnClickListener {
            dialog.dismiss()
        }

        dialog.show()
    }

    override fun onSupportNavigateUp(): Boolean {
        val navController = findNavController(R.id.nav_host_fragment_content_main)
        return navController.navigateUp(appBarConfiguration) || super.onSupportNavigateUp()
    }

    /**
     * Redirects to the LoginActivity and finishes the current activity.
     */
    private fun redirectToLogin() {
        val intent = Intent(this, LoginActivity::class.java)
        startActivity(intent)
        finish()
    }

    /**
     * Sets up action menu item visibility based on user role.
     *
     * @param menu The action menu.
     */
    private fun setupActionMenuVisibility(menu: Menu) {
        val user = sessionManager.getUser()
        val addMenuItem = menu.findItem(R.id.action_add)

        if (addMenuItem?.hasSubMenu() == true) {
            val subMenu = addMenuItem.subMenu

            // Hide New Ticket menu item for superusers and limited admins (show only for regular users)
            val newTicketMenuItem = subMenu?.findItem(R.id.action_new_ticket)
            if (user != null && (user.isSuperuser || user.isLimitedAdmin)) {
                newTicketMenuItem?.isVisible = false
            }

            // Show New Knowledge, New FAQ, and New Event menu items only for superusers
            val newKnowledgeMenuItem = subMenu?.findItem(R.id.action_new_knowledge)
            val newFaqMenuItem = subMenu?.findItem(R.id.action_new_faq)
            val newEventMenuItem = subMenu?.findItem(R.id.action_new_event)

            if (user == null || !user.isSuperuser) {
                newKnowledgeMenuItem?.isVisible = false
                newFaqMenuItem?.isVisible = false
                newEventMenuItem?.isVisible = false
            }

            // Search and logout are always visible (no role restrictions)
            val searchMenuItem = subMenu?.findItem(R.id.action_search)
            val logoutMenuItem = subMenu?.findItem(R.id.action_logout)
            searchMenuItem?.isVisible = true
            logoutMenuItem?.isVisible = true

            // The add menu should always be visible since search and logout are always available
            addMenuItem.isVisible = true
        }
    }
}
