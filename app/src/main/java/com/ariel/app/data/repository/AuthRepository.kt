package com.ariel.app.data.repository

import com.ariel.app.data.api.RetrofitClient
import com.ariel.app.data.model.LoginRequest
import com.ariel.app.data.model.LoginResponse
import com.ariel.app.data.model.User
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import retrofit2.Response

/**
 * Repository class that handles authentication-related operations.
 */
class AuthRepository {
    
    private val apiService = RetrofitClient.getApiService()
    
    /**
     * Attempts to log in a user with the provided credentials.
     *
     * @param username The username for authentication.
     * @param password The password for authentication.
     * @return A Result containing either the LoginResponse or an Exception.
     */
    suspend fun login(username: String, password: String): Result<LoginResponse> {
        return withContext(Dispatchers.IO) {
            try {
                val loginRequest = LoginRequest(username, password)
                val response = apiService.login(loginRequest)
                
                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Login failed: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
    
    /**
     * Extracts a User object from a LoginResponse.
     *
     * @param loginResponse The login response containing user information.
     * @return A User object with the user's details.
     */
    fun getUserFromLoginResponse(loginResponse: LoginResponse): User {
        return User(
            id = loginResponse.id,
            username = loginResponse.username,
            isSuperuser = loginResponse.isSuperuser,
            isLimitedAdmin = loginResponse.isLimitedAdmin
        )
    }
}
