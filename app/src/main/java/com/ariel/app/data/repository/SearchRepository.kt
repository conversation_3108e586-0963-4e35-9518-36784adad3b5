package com.ariel.app.data.repository

import com.ariel.app.data.api.RetrofitClient
import com.ariel.app.data.model.SearchResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Repository class that handles search-related operations.
 */
class SearchRepository {

    private val apiService = RetrofitClient.getApiService()

    /**
     * Searches for content across tickets, events, knowledges, and FAQs.
     *
     * @param token The authentication token.
     * @param query The search query string.
     * @return A Result containing either the search results or an Exception.
     */
    suspend fun search(token: String, query: String): Result<SearchResult> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val response = apiService.search(authToken, query)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to perform search: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }
}
