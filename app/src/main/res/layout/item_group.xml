<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="80dp"
    android:layout_margin="4dp"
    app:cardElevation="0dp"
    app:cardCornerRadius="8dp"
    app:cardBackgroundColor="?attr/colorSurface"
    app:strokeColor="@color/card_border"
    app:strokeWidth="1dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="4dp">

        <TextView
            android:id="@+id/tv_group_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:textStyle="bold"
            android:textColor="@color/statistics_text_color"
            android:gravity="center"
            android:maxLines="2"
            android:ellipsize="end"
            tools:text="بیمارستان" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
