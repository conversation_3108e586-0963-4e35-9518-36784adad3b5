package com.ariel.app.data.repository

import android.content.Context
import android.net.Uri
import android.provider.OpenableColumns
import com.ariel.app.data.api.RetrofitClient
import com.ariel.app.data.model.Event
import com.ariel.app.data.model.EventGroup
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.File
import java.io.FileOutputStream

/**
 * Repository class that handles new event-related operations.
 */
class NewEventRepository {

    private val apiService = RetrofitClient.getApiService()

    /**
     * Retrieves the list of available groups.
     *
     * @param token The authentication token.
     * @return A Result containing either the list of groups or an Exception.
     */
    suspend fun getGroups(token: String): Result<List<EventGroup>> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val response = apiService.getGroups(authToken)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to fetch groups: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Creates a new event.
     *
     * @param token The authentication token.
     * @param title The title of the event.
     * @param body The body content of the event.
     * @param groupId The ID of the group the event belongs to.
     * @return A Result containing either the created event or an Exception.
     */
    suspend fun createEvent(
        token: String,
        title: String,
        body: String,
        groupId: Int
    ): Result<Event> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"
                val eventRequest = mapOf(
                    "title" to title,
                    "body" to body,
                    "group_id" to groupId.toString()
                )

                val response = apiService.createEvent(authToken, eventRequest)

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to create event: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Creates a new event with a file attachment.
     *
     * @param token The authentication token.
     * @param title The title of the event.
     * @param body The body content of the event.
     * @param groupId The ID of the group the event belongs to.
     * @param fileUri The URI of the file to attach.
     * @param context The context to use for accessing the content resolver.
     * @return A Result containing either the created event or an Exception.
     */
    suspend fun createEventWithFile(
        token: String,
        title: String,
        body: String,
        groupId: Int,
        fileUri: Uri,
        context: Context
    ): Result<Event> {
        return withContext(Dispatchers.IO) {
            try {
                val authToken = "Token $token"

                // Convert the URI to a file
                val file = uriToFile(fileUri, context)

                // Create request parts
                val titlePart = title.toRequestBody("text/plain".toMediaTypeOrNull())
                val bodyPart = body.toRequestBody("text/plain".toMediaTypeOrNull())
                val groupIdPart = groupId.toString().toRequestBody("text/plain".toMediaTypeOrNull())

                // Get file MIME type
                val mimeType = context.contentResolver.getType(fileUri) ?: "application/octet-stream"

                // Create file part
                val requestFile = file.asRequestBody(mimeType.toMediaTypeOrNull())
                val filePart = MultipartBody.Part.createFormData("file", file.name, requestFile)

                val response = apiService.createEventWithFile(
                    authToken,
                    titlePart,
                    bodyPart,
                    groupIdPart,
                    filePart
                )

                if (response.isSuccessful && response.body() != null) {
                    Result.success(response.body()!!)
                } else {
                    Result.failure(Exception("Failed to create event with file: ${response.message()}"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }
    }

    /**
     * Converts a URI to a File.
     *
     * @param uri The URI to convert.
     * @param context The context to use for accessing the content resolver.
     * @return The File created from the URI.
     */
    private fun uriToFile(uri: Uri, context: Context): File {
        val inputStream = context.contentResolver.openInputStream(uri)
        val fileName = getFileNameFromUri(uri, context)
        val tempFile = File(context.cacheDir, fileName)

        tempFile.createNewFile()

        FileOutputStream(tempFile).use { outputStream ->
            inputStream?.copyTo(outputStream)
        }

        return tempFile
    }

    /**
     * Gets the file name from a URI.
     *
     * @param uri The URI to get the file name from.
     * @param context The context to use for accessing the content resolver.
     * @return The file name.
     */
    private fun getFileNameFromUri(uri: Uri, context: Context): String {
        var fileName = "file_${System.currentTimeMillis()}"

        context.contentResolver.query(uri, null, null, null, null)?.use { cursor ->
            if (cursor.moveToFirst()) {
                val displayNameIndex = cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME)
                if (displayNameIndex != -1) {
                    fileName = cursor.getString(displayNameIndex)
                }
            }
        }

        return fileName
    }
}
