package com.ariel.app.ui.editfaq

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ariel.app.data.model.FAQ
import com.ariel.app.data.repository.FAQRepository
import kotlinx.coroutines.launch

/**
 * ViewModel for the edit FAQ screen.
 */
class EditFAQViewModel : ViewModel() {

    private val faqRepository = FAQRepository()

    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    private val _error = MutableLiveData<String?>()
    val error: LiveData<String?> = _error

    private val _faqUpdateResult = MutableLiveData<FAQUpdateResult>()
    val faqUpdateResult: LiveData<FAQUpdateResult> = _faqUpdateResult

    /**
     * Updates an existing FAQ item.
     *
     * @param token The authentication token.
     * @param shortUuid The short UUID of the FAQ item to update.
     * @param title The updated title of the FAQ item.
     * @param body The updated content body of the FAQ item.
     */
    fun updateFAQ(token: String, shortUuid: String, title: String, body: String) {
        _isLoading.value = true
        _error.value = null

        viewModelScope.launch {
            try {
                val result = faqRepository.updateFAQ(token, shortUuid, title, body)

                result.fold(
                    onSuccess = { faq ->
                        _faqUpdateResult.value = FAQUpdateResult.Success(faq)
                    },
                    onFailure = { exception ->
                        _faqUpdateResult.value = FAQUpdateResult.Error(
                            exception.message ?: "Failed to update FAQ"
                        )
                    }
                )
            } catch (e: Exception) {
                _faqUpdateResult.value = FAQUpdateResult.Error(
                    e.message ?: "Unknown error occurred"
                )
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Validates the input fields for updating a FAQ item.
     *
     * @param title The title of the FAQ item.
     * @param body The content body of the FAQ item.
     * @return A FAQValidationResult indicating whether the inputs are valid.
     */
    fun validateInputs(title: String, body: String): FAQValidationResult {
        val isTitleValid = title.isNotBlank()
        val isBodyValid = body.isNotBlank()

        return FAQValidationResult(isTitleValid, isBodyValid)
    }

    /**
     * Clears any error messages.
     */
    fun clearError() {
        _error.value = null
    }

    /**
     * Sealed class representing the result of updating a FAQ item.
     */
    sealed class FAQUpdateResult {
        data class Success(val faq: FAQ) : FAQUpdateResult()
        data class Error(val errorMessage: String) : FAQUpdateResult()
    }
}

/**
 * Data class representing the validation result for a FAQ item.
 *
 * @property isTitleValid Whether the title is valid.
 * @property isBodyValid Whether the body is valid.
 */
data class FAQValidationResult(
    val isTitleValid: Boolean,
    val isBodyValid: Boolean
)
