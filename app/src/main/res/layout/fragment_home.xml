<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="6dp"
    tools:context=".ui.home.HomeFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- Section 1: Tickets -->
        <!-- All Tickets Box -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_all_tickets"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp"
            android:layout_marginTop="4dp"
            app:cardElevation="0dp"
            app:cardCornerRadius="8dp"
            app:cardBackgroundColor="?attr/colorSurface"
            app:strokeColor="@color/card_border"
            app:strokeWidth="1dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <LinearLayout
                android:id="@+id/box_all_tickets"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="4dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackground">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="همه تیکت‌ها"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/statistics_text_color"
                    android:gravity="center" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- Dynamic Boxes Container -->
        <LinearLayout
            android:id="@+id/container_dynamic_boxes"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="4dp"
            android:orientation="vertical"
            app:layout_constraintTop_toBottomOf="@id/card_all_tickets"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Loading indicator -->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="visible"
            app:layout_constraintTop_toBottomOf="@id/card_all_tickets"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_marginTop="20dp" />

        <!-- Error message -->
        <TextView
            android:id="@+id/error_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Loading statistics..."
            android:textSize="16sp"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/container_dynamic_boxes"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_margin="4dp"
            android:layout_marginTop="8dp" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.core.widget.NestedScrollView>
