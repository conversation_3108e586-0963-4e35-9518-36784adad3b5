package com.ariel.app.ui.home

import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import com.google.android.material.card.MaterialCardView
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.GridLayoutManager
import com.ariel.app.ArielApplication
import com.ariel.app.R
import com.ariel.app.data.model.TicketStatistics
import com.ariel.app.databinding.FragmentHomeBinding
import com.ariel.app.ui.tickets.TicketAdapter
import java.text.NumberFormat
import java.util.Locale

class HomeFragment : Fragment() {

    private var _binding: FragmentHomeBinding? = null
    private lateinit var homeViewModel: HomeViewModel
    private lateinit var ticketAdapter: TicketAdapter
    private lateinit var groupAdapter: GroupAdapter

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val sessionManager = (requireActivity().application as ArielApplication).sessionManager
        val factory = HomeViewModelFactory(sessionManager)
        homeViewModel = ViewModelProvider(this, factory)[HomeViewModel::class.java]

        _binding = FragmentHomeBinding.inflate(inflater, container, false)
        val root: View = binding.root

        setupRecyclerView()
        setupObservers()

        // Clear filtered tickets section when fragment is created
        homeViewModel.clearFilteredTickets()

        return root
    }

    override fun onResume() {
        super.onResume()
        // refresh homepage data every time user navigates back to homepage
        homeViewModel.refreshHomepageData()
        // clear filtered tickets section when navigating to home
        homeViewModel.clearFilteredTickets()
    }

    private fun setupRecyclerView() {
        // Setup tickets adapter
        ticketAdapter = TicketAdapter { ticket ->
            // Navigate to ticket details
            try {
                val bundle = android.os.Bundle().apply {
                    putString("shortUuid", ticket.shortUuid)
                }
                findNavController().navigate(R.id.action_nav_home_to_ticketDetailsFragment, bundle)
            } catch (e: Exception) {
                Log.e("HomeFragment", "Navigation error", e)
            }
        }

        binding.recyclerFilteredTickets.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = ticketAdapter
        }

        // Setup groups adapter
        groupAdapter = GroupAdapter { group ->
            homeViewModel.fetchTicketsByGroupId(group)
        }

        binding.recyclerGroups.apply {
            // use span count of 2 to handle 1 or 2 items per row
            val gridLayoutManager = GridLayoutManager(requireContext(), 2)
            gridLayoutManager.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    val totalItems = groupAdapter.itemCount
                    if (totalItems == 0) return 1 // default span

                    val itemsInLastRow = totalItems % 2

                    // if we're in the last row and there's only 1 item
                    if (itemsInLastRow == 1 && position == totalItems - 1) {
                        return 2 // single item takes full width (2 spans)
                    }
                    return 1 // normal span size (1 span per item for 2 columns)
                }
            }
            layoutManager = gridLayoutManager
            adapter = groupAdapter
        }
    }

    private fun setupObservers() {
        homeViewModel.ticketStatistics.observe(viewLifecycleOwner) { statistics ->
            updateStatisticsUI(statistics)
        }

        homeViewModel.isLoading.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
            if (!isLoading) {
                binding.errorMessage.visibility = View.GONE
            }
        }

        // Observe filtered tickets
        homeViewModel.filteredTickets.observe(viewLifecycleOwner) { tickets ->
            ticketAdapter.submitList(tickets)

            // Show/hide filtered tickets section
            val hasTickets = tickets.isNotEmpty()
            binding.recyclerFilteredTickets.visibility = if (hasTickets) View.VISIBLE else View.GONE
            binding.tvFilteredTicketsTitle.visibility = if (hasTickets) View.VISIBLE else View.GONE
            binding.tvFilteredTicketsEmpty.visibility = if (!hasTickets && homeViewModel.selectedStatusLabel.value != null) View.VISIBLE else View.GONE
        }

        homeViewModel.isLoadingTickets.observe(viewLifecycleOwner) { isLoading ->
            binding.progressBarTickets.visibility = if (isLoading) View.VISIBLE else View.GONE

            if (isLoading) {
                binding.tvFilteredTicketsError.visibility = View.GONE
                binding.tvFilteredTicketsEmpty.visibility = View.GONE
            }
        }

        homeViewModel.ticketsError.observe(viewLifecycleOwner) { errorMessage ->
            if (errorMessage != null) {
                binding.tvFilteredTicketsError.text = errorMessage
                binding.tvFilteredTicketsError.visibility = View.VISIBLE
                binding.recyclerFilteredTickets.visibility = View.GONE
                binding.tvFilteredTicketsEmpty.visibility = View.GONE
            } else {
                binding.tvFilteredTicketsError.visibility = View.GONE
            }
        }

        homeViewModel.selectedStatusLabel.observe(viewLifecycleOwner) { statusLabel ->
            if (statusLabel != null) {
                binding.tvFilteredTicketsTitle.text = "تیکت‌های: $statusLabel"
            } else {
                binding.tvFilteredTicketsTitle.visibility = View.GONE
                binding.recyclerFilteredTickets.visibility = View.GONE
                binding.tvFilteredTicketsEmpty.visibility = View.GONE
                binding.tvFilteredTicketsError.visibility = View.GONE
                binding.progressBarTickets.visibility = View.GONE
            }
        }

        homeViewModel.selectedGroup.observe(viewLifecycleOwner) { group ->
            if (group != null) {
                binding.tvFilteredTicketsTitle.text = "تیکت‌های: ${group.name}"
                binding.tvFilteredTicketsTitle.visibility = View.VISIBLE
            }
        }

        // Observe groups
        homeViewModel.groups.observe(viewLifecycleOwner) { groups ->
            groupAdapter.submitList(groups)

            // Show/hide groups section
            val hasGroups = groups.isNotEmpty()
            binding.recyclerGroups.visibility = if (hasGroups) View.VISIBLE else View.GONE
        }
    }

    private fun updateStatisticsUI(statistics: List<TicketStatistics>) {
        // hide all new boxes first
        binding.newStatisticsCard1.visibility = View.GONE
        binding.newStatisticsCard2.visibility = View.GONE
        binding.newStatisticsCard3.visibility = View.GONE

        if (statistics.isEmpty()) {
            binding.errorMessage.text = "اطلاعات در دسترس نیست"
            binding.errorMessage.visibility = View.VISIBLE
            return
        }

        // show boxes based on available statistics
        statistics.forEachIndexed { index, stat ->
            when (index) {
                0 -> updateNewStatisticsBox(binding.newStatisticsCard1, binding.newStatisticsBox1, binding.newStatisticsLabel1, binding.newStatisticsCount1, stat)
                1 -> updateNewStatisticsBox(binding.newStatisticsCard2, binding.newStatisticsBox2, binding.newStatisticsLabel2, binding.newStatisticsCount2, stat)
                2 -> updateNewStatisticsBox(binding.newStatisticsCard3, binding.newStatisticsBox3, binding.newStatisticsLabel3, binding.newStatisticsCount3, stat)
            }
        }

        binding.errorMessage.visibility = View.GONE
    }

    private fun updateNewStatisticsBox(
        cardView: MaterialCardView,
        box: LinearLayout,
        labelView: TextView,
        countView: TextView,
        statistics: TicketStatistics
    ) {
        cardView.visibility = View.VISIBLE
        labelView.text = statistics.label

        // convert to persian numerals
        val persianCount = convertToPersianNumerals(statistics.count.toString())
        countView.text = persianCount

        // set colors based on value
        val colors = getNewColorsForValue(statistics.value)
        countView.setTextColor(colors.first)
        cardView.setCardBackgroundColor(colors.second)

        // make box clickable
        box.setOnClickListener {
            homeViewModel.fetchTicketsByStatus(statistics.value, statistics.label)
        }
    }

    private fun getNewColorsForValue(value: String): Pair<Int, Int> {
        return when (value) {
            "P" -> Pair(
                ContextCompat.getColor(requireContext(), R.color.status_p_fg),
                ContextCompat.getColor(requireContext(), R.color.status_p_bg)
            )
            "I" -> Pair(
                ContextCompat.getColor(requireContext(), R.color.status_i_fg),
                ContextCompat.getColor(requireContext(), R.color.status_i_bg)
            )
            "R" -> Pair(
                ContextCompat.getColor(requireContext(), R.color.status_r_fg),
                ContextCompat.getColor(requireContext(), R.color.status_r_bg)
            )
            "unvisited" -> Pair(
                ContextCompat.getColor(requireContext(), R.color.statistics_unvisited_fg),
                ContextCompat.getColor(requireContext(), R.color.statistics_unvisited_bg)
            )
            else -> Pair(
                ContextCompat.getColor(requireContext(), R.color.statistics_text_color),
                ContextCompat.getColor(requireContext(), R.color.surface)
            )
        }
    }

    private fun convertToPersianNumerals(input: String): String {
        val persianDigits = arrayOf('۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹')
        val englishDigits = arrayOf('0', '1', '2', '3', '4', '5', '6', '7', '8', '9')

        var result = input
        for (i in englishDigits.indices) {
            result = result.replace(englishDigits[i], persianDigits[i])
        }
        return result
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
