<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="6dp"
    tools:context=".ui.home.HomeFragment">

<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <!-- New Statistics Boxes -->
    <LinearLayout
        android:id="@+id/new_statistics_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="4dp"
        android:orientation="horizontal"
        android:weightSum="3"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- New First Statistics Box -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/new_statistics_card_1"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_weight="1"
            android:layout_marginEnd="6dp"
            app:cardElevation="0dp"
            app:cardCornerRadius="8dp"
            app:cardBackgroundColor="?attr/colorSurface"
            app:strokeColor="@color/card_border"
            app:strokeWidth="1dp"
            android:visibility="gone">

            <LinearLayout
                android:id="@+id/new_statistics_box_1"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="4dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackground">

                <TextView
                    android:id="@+id/new_statistics_label_1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="@color/statistics_text_color"
                    android:gravity="center"
                    tools:text="در انتظار پاسخ" />

                <TextView
                    android:id="@+id/new_statistics_count_1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:gravity="center"
                    tools:text="۱۳" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- New Second Statistics Box -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/new_statistics_card_2"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_weight="1"
            android:layout_marginStart="3dp"
            android:layout_marginEnd="3dp"
            app:cardElevation="0dp"
            app:cardCornerRadius="8dp"
            app:cardBackgroundColor="?attr/colorSurface"
            app:strokeColor="@color/card_border"
            app:strokeWidth="1dp"
            android:visibility="gone">

            <LinearLayout
                android:id="@+id/new_statistics_box_2"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="4dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackground">

                <TextView
                    android:id="@+id/new_statistics_label_2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="@color/statistics_text_color"
                    android:gravity="center"
                    tools:text="در حال انجام" />

                <TextView
                    android:id="@+id/new_statistics_count_2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:gravity="center"
                    tools:text="۱" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- New Third Statistics Box -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/new_statistics_card_3"
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:layout_weight="1"
            android:layout_marginStart="6dp"
            app:cardElevation="0dp"
            app:cardCornerRadius="8dp"
            app:cardBackgroundColor="?attr/colorSurface"
            app:strokeColor="@color/card_border"
            app:strokeWidth="1dp"
            android:visibility="gone">

            <LinearLayout
                android:id="@+id/new_statistics_box_3"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="4dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackground">

                <TextView
                    android:id="@+id/new_statistics_label_3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:textColor="@color/statistics_text_color"
                    android:gravity="center"
                    tools:text="جدید" />

                <TextView
                    android:id="@+id/new_statistics_count_3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:gravity="center"
                    tools:text="۷" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:layout_constraintTop_toTopOf="@id/new_statistics_container"
        app:layout_constraintBottom_toBottomOf="@id/new_statistics_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Groups Section - Moved to top after statistics -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_groups"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipToPadding="false"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/new_statistics_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="4dp" />



    <TextView
        android:id="@+id/error_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Loading statistics..."
        android:textSize="16sp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/recycler_groups"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_margin="4dp"
        android:layout_marginTop="8dp" />

    <!-- Filtered Tickets Section -->
    <TextView
        android:id="@+id/tv_filtered_tickets_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Filtered Tickets"
        android:textAppearance="?attr/textAppearanceCaption"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/error_message"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_margin="4dp"
        android:layout_marginTop="8dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_filtered_tickets"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:clipToPadding="false"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/tv_filtered_tickets_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:padding="4dp" />
        <!-- NOTE used `android:padding="4dp"` -->
        <!--      instead of `android:layout_margin="4dp"` -->
        <!--      to avoid masking of shadows on the sides -->

    <ProgressBar
        android:id="@+id/progress_bar_tickets"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/tv_filtered_tickets_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="16dp" />

    <TextView
        android:id="@+id/tv_filtered_tickets_error"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textAppearance="?attr/textAppearanceBody1"
        android:textColor="?attr/colorError"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/tv_filtered_tickets_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_margin="4dp"
        android:layout_marginTop="8dp" />

    <TextView
        android:id="@+id/tv_filtered_tickets_empty"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="@string/filtered_tickets_empty"
        android:textAppearance="?attr/textAppearanceBody1"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/tv_filtered_tickets_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_margin="4dp"
        android:layout_marginTop="8dp" />

</androidx.constraintlayout.widget.ConstraintLayout>

</androidx.core.widget.NestedScrollView>
