package com.ariel.app.ui.knowledges

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.ariel.app.data.model.Knowledge
import com.ariel.app.databinding.ItemKnowledgeBinding

/**
 * Adapter for displaying knowledge items in a RecyclerView.
 *
 * @property onKnowledgeClick Callback for when a knowledge item is clicked.
 */
class KnowledgeAdapter(private val onKnowledgeClick: (Knowledge) -> Unit) :
    ListAdapter<Knowledge, KnowledgeAdapter.KnowledgeViewHolder>(KnowledgeDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): KnowledgeViewHolder {
        val binding = ItemKnowledgeBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return KnowledgeViewHolder(binding)
    }

    override fun onBindViewHolder(holder: KnowledgeViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    /**
     * ViewHolder for a knowledge item.
     */
    inner class KnowledgeViewHolder(private val binding: ItemKnowledgeBinding) :
        RecyclerView.ViewHolder(binding.root) {

        init {
            binding.root.setOnClickListener {
                val position = adapterPosition
                if (position != RecyclerView.NO_POSITION) {
                    onKnowledgeClick(getItem(position))
                }
            }
        }

        /**
         * Binds a knowledge item to the ViewHolder.
         *
         * @param knowledge The knowledge item to bind.
         */
        fun bind(knowledge: Knowledge) {
            binding.apply {
                tvKnowledgeTitle.text = knowledge.title
                tvKnowledgeId.text = knowledge.shortUuid
                tvCreatedDate.text = knowledge.createdJalali
            }
        }
    }

    /**
     * DiffUtil callback for comparing knowledge items.
     */
    private class KnowledgeDiffCallback : DiffUtil.ItemCallback<Knowledge>() {
        override fun areItemsTheSame(oldItem: Knowledge, newItem: Knowledge): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Knowledge, newItem: Knowledge): Boolean {
            return oldItem == newItem
        }
    }
}
