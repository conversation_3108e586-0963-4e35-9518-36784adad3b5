package com.ariel.app.data.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

/**
 * Data class representing a knowledge item in the system.
 *
 * @property id The unique identifier for the knowledge item.
 * @property title The title of the knowledge item.
 * @property body The content body of the knowledge item.
 * @property edited Whether the knowledge item has been edited.
 * @property shortUuid The short UUID of the knowledge item.
 * @property created The creation timestamp of the knowledge item.
 * @property createdJalali The creation timestamp in Jalali calendar format.
 * @property updated The last update timestamp of the knowledge item.
 */
@Parcelize
data class Knowledge(
    val id: Int,
    val title: String,
    val body: String,
    val edited: Boolean,
    @SerializedName("short_uuid") val shortUuid: String,
    val created: String,
    @SerializedName("created_jalali") val createdJalali: String,
    val updated: String
) : Parcelable
