package com.ariel.app.ui.knowledges

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.ariel.app.data.model.Knowledge
import com.ariel.app.data.repository.KnowledgeRepository
import kotlinx.coroutines.launch

/**
 * ViewModel for the knowledges screen that handles knowledge data operations with pagination.
 */
class KnowledgesViewModel : ViewModel() {

    private val knowledgeRepository = KnowledgeRepository()

    private val _knowledges = MutableLiveData<List<Knowledge>>()
    val knowledges: LiveData<List<Knowledge>> = _knowledges

    private val _isLoading = MutableLiveData<Boolean>(false)
    val isLoading: LiveData<Boolean> = _isLoading

    private val _isLoadingMore = MutableLiveData<Boolean>(false)
    val isLoadingMore: LiveData<Boolean> = _isLoadingMore

    private val _error = MutableLiveData<String?>(null)
    val error: LiveData<String?> = _error

    private var nextPageUrl: String? = null
    private var currentKnowledges = mutableListOf<Knowledge>()

    /**
     * Fetches the first page of knowledge items.
     *
     * @param token The authentication token.
     */
    fun fetchKnowledges(token: String) {
        _isLoading.value = true
        _error.value = null
        currentKnowledges.clear()

        viewModelScope.launch {
            try {
                val result = knowledgeRepository.getKnowledges(token, 15)

                result.fold(
                    onSuccess = { paginatedResponse ->
                        currentKnowledges.addAll(paginatedResponse.results)
                        _knowledges.value = currentKnowledges.toList()
                        nextPageUrl = paginatedResponse.next
                    },
                    onFailure = { exception ->
                        _error.value = exception.message ?: "Unknown error occurred"
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message ?: "Unknown error occurred"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Loads more knowledge items from the next page if available.
     *
     * @param token The authentication token.
     */
    fun loadMoreKnowledges(token: String) {
        if (nextPageUrl == null || _isLoadingMore.value == true) {
            return
        }

        _isLoadingMore.value = true

        viewModelScope.launch {
            try {
                val result = knowledgeRepository.getKnowledgesFromUrl(token, nextPageUrl!!)

                result.fold(
                    onSuccess = { paginatedResponse ->
                        currentKnowledges.addAll(paginatedResponse.results)
                        _knowledges.value = currentKnowledges.toList()
                        nextPageUrl = paginatedResponse.next
                    },
                    onFailure = { exception ->
                        _error.value = exception.message ?: "Unknown error occurred"
                    }
                )
            } catch (e: Exception) {
                _error.value = e.message ?: "Unknown error occurred"
            } finally {
                _isLoadingMore.value = false
            }
        }
    }

    /**
     * Checks if there are more pages to load.
     *
     * @return True if there are more pages, false otherwise.
     */
    fun hasMorePages(): Boolean {
        return nextPageUrl != null
    }

    /**
     * Clears any error messages.
     */
    fun clearError() {
        _error.value = null
    }
}
